// package com.caidaocloud.vms;
//
// import com.caidaocloud.vms.application.dto.PositionPriceLibraryCreateDto;
// import com.caidaocloud.vms.application.dto.PositionPriceLibrarySalaryRangeDto;
// import com.caidaocloud.vms.application.service.PositionPriceLibraryService;
// import org.junit.Test;
//
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.boot.test.context.SpringBootTest;
//
// import java.math.BigDecimal;
//
// /**
//  * 岗位价格库薪资范围查询测试
//  *
//  * <AUTHOR>
//  * @date 2025/12/10
//  */
// @SpringBootTest
// public class PositionPriceLibrarySalaryRangeTest {
//
//     @Autowired
//     private PositionPriceLibraryService positionPriceLibraryService;
//
//     @Test
//     public void testFindSalaryRangeByPostIdAndOrgId() {
//         // 测试数据准备
//         String testPostId = "test-post-001";
//         String testOrgId = "test-org-001";
//
//         try {
//             // 先创建一个测试价格库记录
//             PositionPriceLibraryCreateDto createDto = new PositionPriceLibraryCreateDto();
//             createDto.setPostId(testPostId);
//             createDto.setOrgId(testOrgId);
//             createDto.setMinSalary(new BigDecimal("8000"));
//             createDto.setMaxSalary(new BigDecimal("15000"));
//
//             positionPriceLibraryService.create(createDto);
//             System.out.println("创建测试价格库记录成功");
//
//             // 测试查找薪资范围
//             PositionPriceLibrarySalaryRangeDto result = positionPriceLibraryService
//                     .findSalaryRangeByPostIdAndOrgId(testPostId, testOrgId);
//
//             System.out.println("查询结果:");
//             System.out.println("岗位ID: " + result.getPostId());
//             System.out.println("岗位名称: " + result.getPostName());
//             System.out.println("组织ID: " + result.getOrgId());
//             System.out.println("组织名称: " + result.getOrgName());
//             System.out.println("是否找到: " + result.isFound());
//
//             if (result.isFound()) {
//                 System.out.println("最低薪资: " + result.getMinSalary());
//                 System.out.println("最高薪资: " + result.getMaxSalary());
//             } else {
//                 System.out.println("未找到对应的价格库记录");
//             }
//
//         } catch (Exception e) {
//             System.err.println("测试执行失败: " + e.getMessage());
//             e.printStackTrace();
//         }
//     }
//
//     @Test
//     public void testFindSalaryRangeNotFound() {
//         // 测试查找不存在的记录
//         String nonExistentPostId = "non-existent-post";
//         String nonExistentOrgId = "non-existent-org";
//
//         PositionPriceLibrarySalaryRangeDto result = positionPriceLibraryService
//                 .findSalaryRangeByPostIdAndOrgId(nonExistentPostId, nonExistentOrgId);
//
//         System.out.println("查找不存在记录的结果:");
//         System.out.println("岗位ID: " + result.getPostId());
//         System.out.println("组织ID: " + result.getOrgId());
//         System.out.println("是否找到: " + result.isFound());
//         System.out.println("消息: " + result.getMessage());
//     }
//
//     @Test
//     public void testFindSalaryRangeWithInvalidParams() {
//         try {
//             // 测试空参数
//             positionPriceLibraryService.findSalaryRangeByPostIdAndOrgId(null, "test-org");
//         } catch (Exception e) {
//             System.out.println("空岗位ID参数测试通过，异常信息: " + e.getMessage());
//         }
//
//         try {
//             // 测试空组织ID
//             positionPriceLibraryService.findSalaryRangeByPostIdAndOrgId("test-post", null);
//         } catch (Exception e) {
//             System.out.println("空组织ID参数测试通过，异常信息: " + e.getMessage());
//         }
//     }
// }
