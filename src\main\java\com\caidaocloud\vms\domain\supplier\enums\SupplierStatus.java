package com.caidaocloud.vms.domain.supplier.enums;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;

public enum SupplierStatus {
    COOPERATING(0, "合作中"),
    TERMINATED(1, "已终止");

    private final int code;
    private final String description;

    SupplierStatus(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public EnumSimple toEnumSimple() {
        EnumSimple enumSimple = new EnumSimple();
        enumSimple.setValue(String.valueOf(this.code));
        return enumSimple;
    }

    public static SupplierStatus fromCode(int code) {
        for (SupplierStatus status : SupplierStatus.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        throw new ServerException("Invalid SupplierStatus code: " + code);
    }
}