package com.caidaocloud.vms.domain.supplier.repository;

import java.util.List;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.vms.application.dto.SupplierQueryDTO;
import com.caidaocloud.vms.domain.supplier.entity.Supplier;
import com.caidaocloud.vms.domain.supplier.entity.SupplierTaxInfo;

public interface SupplierRepository {
	String saveOrUpdate(Supplier supplier);

	Supplier getById(String supplierId);

	PageResult<Supplier> findByPage(String supplierName, SupplierQueryDTO queryDTO);

	void delete(Supplier supplier);

	String saveOrUpdateTaxInfo(SupplierTaxInfo supplier);

	List<Supplier> list(List<String> idList);

	List<Supplier> list();
}
