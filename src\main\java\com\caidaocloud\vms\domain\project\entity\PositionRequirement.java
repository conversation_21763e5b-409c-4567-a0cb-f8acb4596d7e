package com.caidaocloud.vms.domain.project.entity;

import java.util.List;

import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.vms.application.dto.PositionRequirementDto;
import com.caidaocloud.vms.domain.base.entity.BaseEntity;
import com.caidaocloud.vms.domain.project.annotation.DisplayName;
import com.caidaocloud.vms.domain.project.enums.OperationType;
import com.caidaocloud.vms.domain.project.history.DetailedHistoryFormat;
import lombok.AccessLevel;
import lombok.Data;
import lombok.NoArgsConstructor;

import org.springframework.beans.BeanUtils;

@Data
public class PositionRequirement extends DetailedHistoryFormat<PositionRequirement> {

    private String positionId;

    @DisplayName("工作经验")
    private DictSimple workExperience;

    @DisplayName("学历要求")
    private DictSimple education;

    @DisplayName("最低薪资")
    private Integer minSalary;

    @DisplayName("最高薪资")
    private Integer maxSalary;

    @DisplayName("岗位简介")
    private String description;

    @DisplayName("证书与技能")
    private DictSimple skill;

    private Attachment attachment;

    public static String identifier = "entity.vms.PositionRequirement";

    @Override
    public String getEntityIdentifier() {
        return identifier;
    }

    public void update(PositionRequirementDto requirementDto) {
        BeanUtils.copyProperties(requirementDto, this);
        workExperience = new DictSimple();
        education = new DictSimple();
        skill = new DictSimple();
        workExperience.setValue(requirementDto.getWorkExperience());
        education.setValue(requirementDto.getEducation());
        skill.setValue(requirementDto.getSkill());
        super.update();
    }
}