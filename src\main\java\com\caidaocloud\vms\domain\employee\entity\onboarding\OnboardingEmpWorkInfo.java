package com.caidaocloud.vms.domain.employee.entity.onboarding;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.vms.domain.base.entity.BaseEntity;
import lombok.Data;

@Data
public class OnboardingEmpWorkInfo extends BaseEntity {

    private String empId;

    private String workno;

    private String name;

    private String enName;

    private Long hireDate;


    private EnumSimple empStatus;

    private String photo;

    private String leadEmpId;

    private String organize;

    private String organizeTxt;

    private String jobGrade;

    private String job;

    private String jobTxt;

    private String post;

    private String postTxt;

    private EnumSimple probation;

    private Long confirmationDate;

    private EnumSimple confirmationStatus;

    private DictSimple empType;

    private Long leaveDate;

    private EnumSimple workHour;

    private Double divisionAge;

    private Double divisionAgeAdjust;

    private String companyEmail;

    private String workplace;

    private String workplaceTxt;

    private DictSimple joinCompanyWay;

    private String costCenters;

    private String company;

    private String companyTxt;

    private Long expectGraduateDate;

    private String businessLineId;

    private DictSimple contractType;

    private Long trainingDate;

    private Boolean reentry = false;

    private String reentryType;

    private String reentryId;

    private String leaderOrganize;

    private String leaderOrganizeTxt;

    private String leaderPost;

    private String leaderPostTxt;

    private String recruitment;

    private Boolean completed = false;

    public static String identifier = "entity.vms.OnboardingEmpWorkInfo";


    @Override
    public String getEntityIdentifier() {
        return identifier;
    }

}