// package com.caidaocloud.vms.domain.base.service;
//
// import com.caidaocloud.vms.domain.project.enums.WorkflowConfig;
// import com.caidaocloud.workflow.dto.WfMetaCallbackDto;
// import com.caidaocloud.workflow.dto.WfMetaFunDto;
// import com.caidaocloud.workflow.feign.IWfRegisterFeign;
// import org.mockito.InjectMocks;
// import org.mockito.Mock;
//
// import static org.mockito.ArgumentMatchers.any;
// import static org.mockito.Mockito.*;
//
// /**
//  * WorkflowService 测试类
//  *
//  * <AUTHOR>
//  * @date 2025/10/17
//  */
// @ExtendWith(MockitoExtension.class)
// class WorkflowServiceTest {
//
//     @Mock
//     private IWfRegisterFeign wfRegisterFeign;
//
//     @InjectMocks
//     private WorkflowService workflowService;
//
//     @BeforeEach
//     void setUp() {
//         // 模拟安全用户信息
//         // 注意：在实际测试中可能需要设置SecurityUserUtil的mock
//     }
//
//     @Test
//     void testRegisterWithWorkflowConfig() {
//         // Given
//         WorkflowConfig config = WorkflowConfig.PROJECT_MANAGEMENT;
//
//         // When
//         workflowService.register(config);
//
//         // Then
//         // 验证注册了一个function
//         verify(wfRegisterFeign, times(1)).registerFunction(any(WfMetaFunDto.class));
//
//         // 验证注册了3个callback（APPROVE, REJECT, CANCEL）
//         verify(wfRegisterFeign, times(3)).registerCallback(any(WfMetaCallbackDto.class));
//     }
//
//     @Test
//     void testRegisterWithWorkflowConfigPosition() {
//         // Given
//         WorkflowConfig config = WorkflowConfig.PROJECT_POSITION;
//
//         // When
//         workflowService.register(config);
//
//         // Then
//         // 验证注册了一个function
//         verify(wfRegisterFeign, times(1)).registerFunction(any(WfMetaFunDto.class));
//
//         // 验证注册了3个callback（APPROVE, REJECT, CANCEL）
//         verify(wfRegisterFeign, times(3)).registerCallback(any(WfMetaCallbackDto.class));
//     }
//
//     @Test
//     void testDeprecatedRegisterMethod() {
//         // Given
//         String name = "项目管理";
//         String code = "VMS_PROJECT";
//
//         // When
//         workflowService.register(name, code);
//
//         // Then
//         // 验证注册了一个function
//         verify(wfRegisterFeign, times(1)).registerFunction(any(WfMetaFunDto.class));
//
//         // 验证注册了3个callback（通过枚举配置）
//         verify(wfRegisterFeign, times(3)).registerCallback(any(WfMetaCallbackDto.class));
//     }
//
//     @Test
//     void testDeprecatedRegisterMethodWithUnknownCode() {
//         // Given
//         String name = "未知工作流";
//         String code = "UNKNOWN_CODE";
//
//         // When
//         workflowService.register(name, code);
//
//         // Then
//         // 验证只注册了一个function（使用旧的方式）
//         verify(wfRegisterFeign, times(1)).registerFunction(any(WfMetaFunDto.class));
//
//         // 验证没有注册callback
//         verify(wfRegisterFeign, never()).registerCallback(any(WfMetaCallbackDto.class));
//     }
// }
