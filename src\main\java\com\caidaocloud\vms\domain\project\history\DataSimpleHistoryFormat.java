package com.caidaocloud.vms.domain.project.history;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.vms.domain.base.entity.BaseEntity;
import com.caidaocloud.vms.domain.project.dto.FormatResult;
import com.caidaocloud.vms.domain.project.entity.PositionSupplier;
import com.caidaocloud.vms.domain.project.entity.ProjectChange;
import com.caidaocloud.vms.domain.project.entity.ProjectDraft;
import com.googlecode.totallylazy.Option;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * <AUTHOR>
 * @date 2025/10/11
 */
@Slf4j
public abstract class DataSimpleHistoryFormat<T> extends BaseEntity implements HistoryFormat<T> {

	@Override
	public List<ProjectChange> format(ProjectDraft draft) {
		return format((T) this, draft);
	}

	public <E> FormatResult<E> format(DataSimpleHistoryFormat data, List<ProjectDraft> draftList,
			Class<E> clazz) {
		List<ProjectChange> changeList = new ArrayList<>();
		Option<ProjectDraft> draftOption = Sequences.sequence(draftList)
				.find(d -> data.getBid().equals(d.getTargetId()));
		if (draftOption.isDefined()) {
			changeList.addAll(data.format(draftOption.get()));
			E entity = FastjsonUtil.toObject(draftOption.get().getSnapshot(), clazz);
			return FormatResult.of(entity, changeList, draftOption.get().getBid());
		}
		return FormatResult.empty((E) data);
	}

	/**
	 * 反序列化草稿中的实体数据
	 */
	public T deserializeDraftEntity(String json) {
		try {
			Class<T> entityClass = getEntityClass();
			return FastjsonUtil.toObject(json, entityClass);
		} catch (Exception e) {
			log.error("反序列化草稿实体失败", e);
			throw new RuntimeException("反序列化草稿实体失败", e);
		}
	}

	/**
	 * 获取实体类型
	 * 子类需要返回具体的实体类型
	 */
	public Class<T> getEntityClass() {
		// 获取当前类的泛型父类信息
		ParameterizedType parameterizedType = (ParameterizedType) this.getClass().getGenericSuperclass();
		// 获取实际类型参数数组，索引 0 对应 T
		Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
		if (actualTypeArguments.length > 0) {
			return (Class<T>) actualTypeArguments[0]; // 返回 T 的 Class
		}
		return null; // 无泛型参数
	}
}
