# ProjectDraft实现方案

## 📋 概述

本文档描述了ProjectDraft（项目草稿）的完整实现方案。ProjectDraft用于在工作流开启时保存实体的变更草稿，支持根据实体的bid进行创建或更新操作，实现了草稿的完整生命周期管理。

## 🎯 设计目标

1. **草稿管理**：在工作流开启时，将实体变更保存为草稿而非直接入库
2. **智能更新**：根据实体的bid判断是创建新草稿还是更新现有草稿
3. **临时ID生成**：为新增实体自动生成临时ID
4. **JSON快照**：将实体序列化为JSON格式保存
5. **完整CRUD**：提供草稿的增删改查功能

## 🏗️ 架构设计

### 核心组件

#### 1. ProjectDraft实体
```java
@Data
public class ProjectDraft extends BaseEntity {
    private String targetId;      // 目标实体ID
    private String projectId;     // 项目ID
    private EnumSimple type;      // 历史类型（HistoryType）
    private EnumSimple operation; // 操作类型（OperationType）
    private String snapshot;      // 实体JSON快照
}
```

#### 2. ProjectDraftRepository接口
提供草稿的数据访问功能：
- `saveOrUpdate()` - 保存或更新草稿
- `getById()` - 根据ID查询草稿
- `getByTargetId()` - 根据目标实体ID查询草稿
- `getByProjectIdAndType()` - 根据项目ID和类型查询草稿列表
- `delete()` - 删除草稿
- `deleteByTargetId()` - 根据目标实体ID删除草稿
- `deleteByProjectId()` - 根据项目ID删除所有草稿

#### 3. ProjectDraftRepositoryImpl实现
基于DataQuery框架的Repository实现，提供高效的数据访问。

#### 4. ProjectHistoryFactory草稿创建
```java
@Component
public class ProjectHistoryFactory {
    public void createDraft(String projectId, HistoryType historyType, 
                           OperationType operationType, Object entity) {
        // 草稿创建逻辑
    }
}
```

## 🔧 实现细节

### 草稿创建流程

1. **获取实体ID**：从实体中提取bid作为targetId
2. **临时ID生成**：如果是新增操作且没有bid，生成临时ID
3. **检查现有草稿**：根据targetId查询是否已存在草稿
4. **创建或更新**：
   - 存在草稿：更新现有草稿
   - 不存在草稿：创建新草稿
5. **序列化快照**：将实体序列化为JSON保存
6. **保存草稿**：调用Repository保存草稿

### 临时ID生成策略

```java
// 为新增实体生成临时ID
if (targetId == null && operationType == OperationType.CREATE) {
    targetId = "temp_" + SnowUtil.nextId();
    if (entity instanceof DataSimple) {
        ((DataSimple) entity).setBid(targetId);
    }
}
```

### HistoryAspect集成

在HistoryAspect中集成草稿创建：

```java
// 开启流程时，生成draft
if (HistoryContext.getHistoryContextInfo().isWorkflowEnabled()) {
    HistoryContext.HistoryContextInfo info = HistoryContext.getHistoryContextInfo();
    projectHistoryFactory.createDraft(info.getProjectId(), info.getHistoryType(), 
                                     operationType, entity);
}
```

## 📁 文件结构

### 新增文件
```
src/main/java/com/caidaocloud/vms/
├── domain/project/repository/
│   └── ProjectDraftRepository.java                   # ProjectDraft Repository接口
├── infrastructure/repository/
│   └── ProjectDraftRepositoryImpl.java               # ProjectDraft Repository实现
└── test/java/com/caidaocloud/vms/
    └── ProjectDraftTest.java                         # ProjectDraft功能测试
```

### 修改文件
```
src/main/java/com/caidaocloud/vms/
├── domain/project/factory/
│   └── ProjectHistoryFactory.java                    # 添加createDraft方法，改为Spring组件
├── domain/project/aspect/
│   └── HistoryAspect.java                            # 集成草稿创建逻辑
└── domain/project/entity/
    └── ProjectDraft.java                             # 已存在的实体类
```

## 🧪 测试验证

### 测试用例

1. **testProjectDraftCrud**：验证ProjectDraft的基本CRUD操作
2. **testCreateDraftWithFactory**：验证ProjectHistoryFactory的createDraft方法
3. **testCreateDraftWithTempId**：验证新增实体时的临时ID生成
4. **testDeleteByProjectId**：验证根据项目ID删除所有草稿

### 运行测试
```bash
mvn test -Dtest=ProjectDraftTest
```

## 🚀 使用指南

### 1. 创建草稿

```java
@Autowired
private ProjectHistoryFactory projectHistoryFactory;

// 创建草稿
ProjectPosition position = new ProjectPosition();
position.setBid("position_123");
position.setProjectId("project_456");
// ... 设置其他属性

projectHistoryFactory.createDraft("project_456", HistoryType.POSITION, 
                                 OperationType.CREATE, position);
```

### 2. 查询草稿

```java
@Autowired
private ProjectDraftRepository projectDraftRepository;

// 根据targetId查询草稿
Optional<ProjectDraft> draft = projectDraftRepository.getByTargetId("position_123");

// 根据项目ID和类型查询草稿列表
List<ProjectDraft> drafts = projectDraftRepository.getByProjectIdAndType(
    "project_456", HistoryType.POSITION);
```

### 3. 更新草稿

```java
// 修改实体后再次调用createDraft会自动更新现有草稿
position.setPosition("更新后的岗位名称");
projectHistoryFactory.createDraft("project_456", HistoryType.POSITION, 
                                 OperationType.UPDATE, position);
```

### 4. 删除草稿

```java
// 根据targetId删除草稿
projectDraftRepository.deleteByTargetId("position_123");

// 根据项目ID删除所有草稿
projectDraftRepository.deleteByProjectId("project_456");
```

## 🎨 设计优势

### 1. 智能判断
- 自动根据实体的bid判断是创建还是更新草稿
- 为新增实体自动生成临时ID
- 避免重复创建草稿

### 2. 完整生命周期
- 支持草稿的创建、查询、更新、删除
- 提供多种查询方式（ID、targetId、项目ID+类型）
- 支持批量删除操作

### 3. JSON快照
- 将实体完整序列化为JSON保存
- 保留实体的所有状态信息
- 便于后续反序列化和恢复

### 4. 集成友好
- 与现有的HistoryAspect无缝集成
- 不影响原有的工作流逻辑
- 可扩展支持更多实体类型

## 📝 注意事项

1. **实体ID处理**：确保实体实现了DataSimple接口，以便获取和设置bid
2. **JSON序列化**：实体类需要支持Jackson序列化/反序列化
3. **临时ID格式**：临时ID以"temp_"开头，便于识别和处理
4. **事务管理**：草稿操作应在事务中进行，确保数据一致性
5. **内存管理**：定期清理不需要的草稿，避免数据积累

## 🔮 未来扩展

1. **草稿版本管理**：支持草稿的多版本保存
2. **草稿比较功能**：提供草稿与原始数据的对比
3. **草稿过期机制**：自动清理过期的草稿
4. **草稿合并功能**：支持多个草稿的合并操作
5. **草稿审计日志**：记录草稿的操作历史

## 📊 性能考虑

1. **索引优化**：为targetId、projectId、type等字段建立索引
2. **批量操作**：提供批量创建和删除草稿的接口
3. **缓存策略**：对频繁查询的草稿进行缓存
4. **分页查询**：对大量草稿的查询提供分页支持

---

这个ProjectDraft实现方案提供了完整的草稿管理功能，与工作流系统无缝集成，支持智能的创建/更新判断和临时ID生成，为复杂的业务场景提供了强大的草稿支持能力。
