package com.caidaocloud.vms.domain.project.factory;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.vms.application.dto.ProjectDto;
import com.caidaocloud.vms.domain.project.entity.Project;
import com.caidaocloud.vms.domain.project.enums.ProjectStatus;
import org.apache.commons.collections.Factory;

import org.springframework.beans.BeanUtils;

public class ProjectFactory{
    public static Project create(ProjectDto projectDto, boolean checkWorkflowEnable) {
        Project project = new Project(projectDto.getProjectCode(), projectDto.getProjectName());
        if (projectDto.getBid() != null) {
            // 使用HistoryAspect中生成的bid
            project.setBid(projectDto.getBid());
        }
        // 设置项目其他属性
        BeanUtils.copyProperties(projectDto, project);
        project.setProjectManager(new EmpSimple());
        project.getProjectManager().setEmpId(projectDto.getProjectManager());
        // 未配置工作流时，保存后为通过状态
        if (!checkWorkflowEnable) {
            project.setStatus(ProjectStatus.APPROVED.toEnumSimple());
        }
        return project;
    }
}
