package com.caidaocloud.vms.infrastructure.repository.base;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.vms.application.dto.base.PostInfoDto;
import com.caidaocloud.vms.domain.base.repository.PostRepository;
import com.caidaocloud.vms.infrastructure.util.DataUtil;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Sequences;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR>
 * @date 2025/10/15
 */
@Repository
public class PostRepositoryImpl implements PostRepository {

	@Override
	public List<PostInfoDto> loadPostList(List<String> postIds) {
		postIds = Sequences.sequence(postIds).filter(Objects::nonNull).toList();
		if (CollectionUtils.isEmpty(postIds)) {
			return new ArrayList<>();
		}
		List<DataSimple> list = DataQuery.identifier("entity.hr.Post").limit(-1, 1)
				.filter(DataFilter.in("bid", postIds).andNe("deleted", Boolean.TRUE.toString()), DataSimple.class)
				.getItems();

		return Sequences.sequence(list).map(data -> DataUtil.convert(data, PostInfoDto.class)).toList();
	}

	@Override
	public List<String> regexPostId(String keyword) {
		List<Map<String, String>> regex = DataQuery.identifier("entity.hr.Post").limit(-1, 1)
				.filterProperties(DataFilter.regex("name", keyword).orRegex("code", keyword).and(
						DataFilter.ne("deleted", Boolean.TRUE.toString())), Lists.list("bid"), System.currentTimeMillis())
				.getItems();

		return Sequences.sequence(regex).map(map -> map.get("bid")).toList();
	}

	@Override
	public Optional<PostInfoDto> loadPost(String position) {
		if (StringUtils.isEmpty(position)) {
			return Optional.empty();
		}

		return Optional.ofNullable(DataQuery.identifier("entity.hr.Post").oneOrNull(position, DataSimple.class))
				.map(data -> DataUtil.convert(data, PostInfoDto.class));
	}

}
