package com.caidaocloud.vms.application.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Min;

/**
 * 岗位价格库修改DTO
 *
 * <AUTHOR>
 * @date 2025/12/10
 */
@Data
@ApiModel(description = "岗位价格库修改DTO")
public class PositionPriceLibraryUpdateDto {

    @ApiModelProperty(value = "主键ID", required = true)
    @NotBlank(message = "主键ID不能为空")
    private String bid;

    @ApiModelProperty(value = "最低薪资", required = true)
    @NotNull(message = "最低薪资不能为空")
    @Min(value = 0, message = "最低薪资不能小于0")
    private Integer minSalary;

    @ApiModelProperty(value = "最高薪资", required = true)
    @NotNull(message = "最高薪资不能为空")
    @Min(value = 0, message = "最高薪资不能小于0")
    private Integer maxSalary;
}
