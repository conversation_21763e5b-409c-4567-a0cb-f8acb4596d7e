package com.caidaocloud.vms.domain.base.exception;

/**
 * 工作流启动异常
 * 当工作流启动失败时抛出此异常，用于触发手动回滚操作
 *
 * <AUTHOR>
 * @date 2025/10/17
 */
public class WorkflowStartException extends RuntimeException {

    public WorkflowStartException(String message) {
        super(message);
    }

    public WorkflowStartException(String message, Throwable cause) {
        super(message, cause);
    }

    public WorkflowStartException(Throwable cause) {
        super(cause);
    }
}
