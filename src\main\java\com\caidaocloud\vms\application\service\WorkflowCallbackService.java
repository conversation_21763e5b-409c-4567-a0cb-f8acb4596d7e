package com.caidaocloud.vms.application.service;

import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.annotation.PaasTransactional;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.vms.domain.base.enums.ApprovalStatus;
import com.caidaocloud.vms.domain.project.entity.*;
import com.caidaocloud.vms.domain.project.enums.HistoryType;
import com.caidaocloud.vms.domain.project.enums.OperationType;
import com.caidaocloud.vms.domain.project.enums.ProjectStatus;
import com.caidaocloud.vms.domain.project.enums.WorkflowConfig;
import com.caidaocloud.vms.domain.project.repository.*;
import com.caidaocloud.workflow.enums.WfCallbackTriggerOperationEnum;
import com.googlecode.totallylazy.Option;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;

/**
 * 工作流回调服务
 * 处理工作流审批结果的业务逻辑
 *
 * <AUTHOR> Zhou
 * @date 2025/10/17
 */
@Service
@Slf4j
public class WorkflowCallbackService {

    @Autowired
    private ProjectHistoryService projectHistoryService;

    @Autowired
    private ProjectHistoryRepository projectHistoryRepository;

    @Autowired
    private ProjectDraftRepository projectDraftRepository;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private ProjectPositionRepository projectPositionRepository;

    @Autowired
    private ProjectContactRepository projectContactRepository;

    @Autowired
    private ProjectSupplierRepository projectSupplierRepository;

    @Autowired
    private PositionSupplierRepository positionSupplierRepository;
    @Autowired
    private PositionRequirementRepository positionRequirementRepository;

    /**
     * 处理项目工作流审批通过
     *
     * @param history 业务单据
     */
    @PaasTransactional
    public void approveProjectWorkflow(ProjectHistory history) {
        try {
            log.info("开始处理项目工作流审批通过，单据id：{}，项目ID: {}", history.getBid(), history.getProjectId());

            Project project = projectRepository.getById(history.getProjectId());
            project.approve();
            projectRepository.saveOrUpdate(project);
            // TODO: 2025/10/24  是否修改所有岗位的审批状态
            List<ProjectDraft> drafts = loadHistoryDraftList(history);
            if (drafts.isEmpty()) {
                log.warn("未找到项目相关的草稿数据，单据id：{}，项目ID: {}", history.getBid(), history.getProjectId());
                return;
            }

            // TODO: 2025/10/24 审批成功后，将history相关草稿删除
            // 清理草稿数据
            projectDraftRepository.deleteByProjectId(history.getProjectId());

            log.info("项目工作流审批通过处理完成，单据id：{}，项目ID: {}", history.getBid(), history.getProjectId());

        } catch (Exception e) {
            log.error("处理项目工作流审批通过失败，业务单据ID: {}", history.getBid(), e);
            throw new ServerException("处理项目工作流审批通过失败", e);
        }
    }

    /**
     * 处理项目工作流审批拒绝
     *
     * @param history 业务单据
     */
    @PaasTransactional
    public void rejectProjectWorkflow(ProjectHistory history) {
        try {
            log.info("开始处理项目工作流审批拒绝，单据id：{}，项目ID: {}", history.getBid(), history.getProjectId());
            Project project = projectRepository.getById(history.getProjectId());
            project.reject();
            projectRepository.saveOrUpdate(project);
            // TODO: 2025/10/24  是否修改所有岗位的审批状态
            // List<ProjectDraft> drafts = loadHistoryDraftList(history);
            // if (drafts.isEmpty()) {
            //     log.warn("未找到项目相关的草稿数据，单据id：{}，项目ID: {}", history.getBid(), history.getProjectId());
            //     return;
            // }

            /* 20251024 审批拒绝后，保留变更，可编辑后重新提交审批 */
            // // 处理拒绝逻辑：对于CREATE操作的草稿，删除对应的模型数据
            // for (ProjectDraft draft : drafts) {
            //     processDraftRejection(draft);
            // }

            // 清理草稿数据
            // projectDraftRepository.deleteByProjectId(history.getProjectId());

            log.info("项目工作流审批拒绝处理完成，单据id：{}，项目ID: {}", history.getBid(), history.getProjectId());

        } catch (Exception e) {
            log.error("处理项目工作流审批拒绝失败，业务单据ID: {}", history.getBid(), e);
            throw new ServerException("处理项目工作流审批拒绝失败", e);
        }
    }

    /**
     * 处理项目岗位工作流审批通过
     *
     * @param history 业务单据
     */
    @PaasTransactional
    public void approveProjectPositionWorkflow(ProjectHistory history) {
        try {
            log.info("开始处理项目岗位工作流审批通过，单据id：{}，岗位id: {}", history.getBid(), history.getPositionId());
            Optional<ProjectPosition> position = projectPositionRepository.getPosition(history.getPositionId());
            if (!position.isPresent()) {
                log.error("岗位审批回调失败，岗位不存在，单据id:{},岗位id:{}", history.getBid(), history.getPositionId());
                return;
            }
            position.get().approve();
            projectPositionRepository.saveOrUpdate(position.get());

            List<ProjectDraft> drafts = loadHistoryDraftList(history);
            if (drafts.isEmpty()) {
                log.warn("未找到项目相关的草稿数据，单据id：{}，项目ID: {}", history.getBid(), history.getProjectId());
                return;
            }

            // // 处理岗位草稿
            // for (ProjectDraft draft : drafts) {
            //     processPositionDraftApproval(draft);
            // }

            // 清理岗位草稿数据
            for (ProjectDraft draft : drafts) {
                projectDraftRepository.delete(draft.getBid());
            }

            log.info("项目岗位工作流审批通过处理完成，单据id：{}，项目ID: {}", history.getBid(), history.getProjectId());

        } catch (Exception e) {
            log.error("处理项目岗位工作流审批通过失败，业务单据ID: {}", history.getBid(), e);
            throw new ServerException("处理项目岗位工作流审批通过失败", e);
        }
    }

    @Deprecated
    private void processPositionDraftApproval(ProjectDraft draft) {
        try {
            OperationType operationType = OperationType.fromValue(draft.getOperation().getValue());
            HistoryType historyType = HistoryType.fromValue(draft.getSubType().getValue());
            String snapshot = draft.getSnapshot();

            log.info("处理草稿审批通过，类型: {}, targetId: {}", historyType, draft.getTargetId());

            switch (historyType) {
            case BASIC_INFO:
                // 处理项目基本信息
                ProjectPosition project = FastjsonUtil.toObject(snapshot, ProjectPosition.class);
                if (operationType == OperationType.DELETE) {
                    projectPositionRepository.deletePosition(project);
                }
                else {
                    projectPositionRepository.saveOrUpdate(project);
                }
                log.info("保存岗位基本信息成功，项目ID: {}", project.getBid());
                break;

            case REQUIRE:
                // 处理项目岗位
                PositionRequirement requirement = FastjsonUtil.toObject(snapshot, PositionRequirement.class);
                positionRequirementRepository.saveOrUpdate(requirement);
                log.info("保存岗位招聘要求成功，岗位ID: {}", requirement.getBid());
                break;

            case SUPPLIER:
                // 处理项目联系人
                PositionSupplier supplier = FastjsonUtil.toObject(snapshot, PositionSupplier.class);
                if (operationType == OperationType.DELETE) {
                    positionSupplierRepository.deleteRelation(supplier);
                }
                else {
                    positionSupplierRepository.saveOrUpdate(supplier);
                }
                log.info("保存岗位联系人成功，联系人ID: {}", supplier.getBid());
                break;

            default:
                log.warn("未知的历史类型: {}", historyType);
                break;
            }

        } catch (Exception e) {
            log.error("处理草稿审批通过失败，草稿ID: {}", draft.getBid(), e);
            throw new RuntimeException("处理草稿审批通过失败", e);
        }
    }

    private List<ProjectDraft> loadHistoryDraftList(ProjectHistory history) {
        List<ProjectHistoryDetail> list = projectHistoryService.loadDetail(history.getBid());
        List<String> draftIds = Sequences.sequence(list).flatMap(ProjectHistoryDetail::getDraft).toList();
        // 获取项目相关的所有草稿
        return projectDraftRepository.listByIds(draftIds);
    }

    /**
     * 处理项目岗位工作流审批拒绝
     *
     * @param history 业务单据
     */
    @PaasTransactional
    public void rejectProjectPositionWorkflow(ProjectHistory history) {
        try {
            log.info("开始处理项目岗位工作流审批拒绝，单据id：{}，项目ID: {}", history.getBid(), history.getProjectId());
            Optional<ProjectPosition> position = projectPositionRepository.getPosition(history.getPositionId());
            if (!position.isPresent()) {
                log.error("岗位审批回调失败，岗位不存在，单据id:{},岗位id:{}", history.getBid(), history.getPositionId());
                return;
            }
            position.get().reject();
            projectPositionRepository.saveOrUpdate(position.get());
            // List<ProjectDraft> drafts = loadHistoryDraftList(history);
            // if (drafts.isEmpty()) {
            //     log.warn("未找到项目相关的草稿数据，单据id：{}，项目ID: {}", history.getBid(), history.getProjectId());
            //     return;
            // }
            //
            // // 处理拒绝逻辑
            // for (ProjectDraft draft : drafts) {
            //     processPositionDraftRejection(draft);
            // }

            // // 清理岗位草稿数据
            // for (ProjectDraft draft : drafts) {
            //     projectDraftRepository.delete(draft.getBid());
            // }

            log.info("项目岗位工作流审批拒绝处理完成，单据id：{}，项目ID: {}", history.getBid(), history.getProjectId());

        } catch (Exception e) {
            log.error("处理项目岗位工作流审批拒绝失败，业务单据ID: {}", history.getBid(), e);
            throw new ServerException("处理项目岗位工作流审批拒绝失败", e);
        }
    }

    @Deprecated
    private void processPositionDraftRejection(ProjectDraft draft) {
        try {
            OperationType operationType = OperationType.fromValue(draft.getOperation().getValue());
            HistoryType historyType = HistoryType.fromValue(draft.getSubType().getValue());

            log.info("处理草稿审批拒绝，类型: {}, 操作: {}, targetId: {}",
                    historyType, operationType, draft.getTargetId());

            // 只有CREATE操作才需要删除模型数据
            if (operationType == OperationType.CREATE) {
                String targetId = draft.getTargetId();

                switch (historyType) {
                case BASIC_INFO:
                    // 删除项目岗位
                    Optional<ProjectPosition> position = projectPositionRepository.getPosition(targetId);
                    if (position.isPresent()) {
                        projectPositionRepository.deletePosition(position.get());
                        positionRequirementRepository.deleteByPositionId(position.get().getBid());
                        log.info("删除项目岗位成功，岗位ID: {}", targetId);
                    }
                    break;
                case SUPPLIER:
                    // 删除项目供应商
                    Optional<PositionSupplier> supplierOpt = positionSupplierRepository.getById(targetId);
                    if (supplierOpt.isPresent()) {
                        positionSupplierRepository.deleteRelation(supplierOpt.get());
                        log.info("删除项目供应商成功，供应商ID: {}", targetId);
                    }
                    break;

                case POSITION:
                default:
                    log.warn("CREATE操作的拒绝处理暂不支持类型: {}", historyType);
                    break;
                }
            } else {
                log.info("非CREATE操作，无需删除模型数据，操作类型: {}", operationType);
            }

        } catch (Exception e) {
            log.error("处理草稿审批拒绝失败，草稿ID: {}", draft.getBid(), e);
            throw new RuntimeException("处理草稿审批拒绝失败", e);
        }
    }

    /**
     * 处理草稿审批通过：将草稿数据保存到对应模型
     *
     * @param draft 草稿数据
     */
    @Deprecated
    private void processDraftApproval(ProjectDraft draft) {
        try {
            OperationType operationType = OperationType.fromValue(draft.getOperation().getValue());
            HistoryType historyType = HistoryType.fromValue(draft.getType().getValue());
            String snapshot = draft.getSnapshot();

            log.info("处理草稿审批通过，类型: {}, targetId: {}", historyType, draft.getTargetId());

            switch (historyType) {
                case BASIC_INFO:
                    // 处理项目基本信息
                    Project project = FastjsonUtil.toObject(snapshot, Project.class);
                    if (operationType == OperationType.DELETE) {
                        projectRepository.delete(project);
                    }
                    else {
                        projectRepository.saveOrUpdate(project);
                    }
                    log.info("保存项目基本信息成功，项目ID: {}", project.getBid());
                    break;

                case POSITION:
                    // 处理项目岗位
                    ProjectPosition position = FastjsonUtil.toObject(snapshot, ProjectPosition.class);
                    if (operationType == OperationType.DELETE) {
                        projectPositionRepository.deletePosition(position);
                    }
                    else {
                        projectPositionRepository.saveOrUpdate(position);
                    }
                    log.info("保存项目岗位成功，岗位ID: {}", position.getBid());
                    break;

                case CONTACT:
                    // 处理项目联系人
                    ProjectContact contact = FastjsonUtil.toObject(snapshot, ProjectContact.class);
                    if (operationType == OperationType.DELETE) {
                        projectContactRepository.deleteContact(contact);
                    }
                    else {
                        projectContactRepository.saveOrUpdate(contact);
                    }
                    log.info("保存项目联系人成功，联系人ID: {}", contact.getBid());
                    break;

                case SUPPLIER:
                    // 处理项目供应商
                    ProjectSupplier supplier = FastjsonUtil.toObject(snapshot, ProjectSupplier.class);
                    if (operationType == OperationType.DELETE) {
                        projectSupplierRepository.deleteSupplier(supplier);
                    }
                    else {
                        projectSupplierRepository.saveOrUpdate(supplier);
                    }
                    log.info("保存项目供应商成功，供应商ID: {}", supplier.getBid());
                    break;

                default:
                    log.warn("未知的历史类型: {}", historyType);
                    break;
            }

        } catch (Exception e) {
            log.error("处理草稿审批通过失败，草稿ID: {}", draft.getBid(), e);
            throw new RuntimeException("处理草稿审批通过失败", e);
        }
    }

    /**
     * 处理草稿审批拒绝：对于CREATE操作，删除对应的模型数据
     *
     * @param draft 草稿数据
     */
    @Deprecated
    private void processDraftRejection(ProjectDraft draft) {
        try {
            OperationType operationType = OperationType.fromValue(draft.getOperation().getValue());
            HistoryType historyType = HistoryType.fromValue(draft.getType().getValue());

            log.info("处理草稿审批拒绝，类型: {}, 操作: {}, targetId: {}",
                    historyType, operationType, draft.getTargetId());

            // 只有CREATE操作才需要删除模型数据
            // TODO: 2025/10/24 update或delete时，根据origin回退数据
            if (operationType == OperationType.CREATE) {
                String targetId = draft.getTargetId();

                switch (historyType) {
                case BASIC_INFO:
                    // 删除项目岗位
                    Project project = projectRepository.getById(targetId);
                    if (project!=null) {
                        projectRepository.delete(project);
                        log.info("删除项目岗位成功，岗位ID: {}", targetId);
                    }
                    break;
                    case POSITION:
                        // 删除项目岗位
                        Optional<ProjectPosition> positionOpt = projectPositionRepository.getPosition(targetId);
                        if (positionOpt.isPresent()) {
                            projectPositionRepository.deletePosition(positionOpt.get());
                            log.info("删除项目岗位成功，岗位ID: {}", targetId);
                        }
                        break;

                    case CONTACT:
                        // 删除项目联系人
                        Optional<ProjectContact> contactOpt = projectContactRepository.getContact(targetId);
                        if (contactOpt.isPresent()) {
                            projectContactRepository.deleteContact(contactOpt.get());
                            log.info("删除项目联系人成功，联系人ID: {}", targetId);
                        }
                        break;

                    case SUPPLIER:
                        // 删除项目供应商
                        Optional<ProjectSupplier> supplierOpt = projectSupplierRepository.getSupplier(targetId);
                        if (supplierOpt.isPresent()) {
                            projectSupplierRepository.deleteSupplier(supplierOpt.get());
                            log.info("删除项目供应商成功，供应商ID: {}", targetId);
                        }
                        break;

                    default:
                        log.warn("CREATE操作的拒绝处理暂不支持类型: {}", historyType);
                        break;
                }
            } else {
                log.info("非CREATE操作，无需删除模型数据，操作类型: {}", operationType);
            }

        } catch (Exception e) {
            log.error("处理草稿审批拒绝失败，草稿ID: {}", draft.getBid(), e);
            throw new RuntimeException("处理草稿审批拒绝失败", e);
        }
    }

    @PaasTransactional
    public void callback(String businessKey, String tenantId, WfCallbackTriggerOperationEnum callback) {
// 设置回调用户信息
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setTenantId(tenantId);
        // 回调默认用户id为 0
        userInfo.setUserId(0L);
        SecurityUserUtil.setSecurityUserInfo(userInfo);
        try {
            SpringUtil.getBean(WorkflowCallbackService.class).doCallback(businessKey, callback);
        } catch (Exception e) {
            throw new ServerException("callback occurs error", e);
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
        }
    }

    public void doCallback(String businessKey, WfCallbackTriggerOperationEnum callback) {
        String businessType = StringUtils.substringBeforeLast(businessKey, "_");
        WorkflowConfig type = WorkflowConfig.fromCode(businessType);
        Optional<ProjectHistory> projectHistoryOptional = projectHistoryService.loadByBusinessKey(businessKey);
        PreCheck.preCheckArgument(!projectHistoryOptional.isPresent(), "apply not exist");

        ProjectHistory history = projectHistoryOptional.get();

        if (!String.valueOf(ApprovalStatus.PENDING.getCode()).equals(history.getApproveStatus().getValue())) {
            log.warn("Project workflow process is ended,businessKey={},status={}", businessKey, history.getApproveStatus());
            return;
        }
        switch (callback) {
        case APPROVED:
            // TODO: 2025/11/4 修改historyDetail状态
            history.setApproveStatus(ApprovalStatus.APPROVED.toEnumSimple());
            history.update();
            if (type == WorkflowConfig.PROJECT_MANAGEMENT) {
                approveProjectWorkflow(history);
            }
            else {
                approveProjectPositionWorkflow(history);
            }
            break;
        case REFUSED:
            history.setApproveStatus(ApprovalStatus.REJECTED.toEnumSimple());
            history.update();
            if (type == WorkflowConfig.PROJECT_MANAGEMENT) {
                rejectProjectWorkflow(history);
            }
            else {
                rejectProjectPositionWorkflow(history);
            }
            break;
        case ERROR:
        case TIMED_TASK:
        case REVOKE:
        default:
            // 回调类型不支持
            throw new ServerException("callback type not support");
        }
    }
}
