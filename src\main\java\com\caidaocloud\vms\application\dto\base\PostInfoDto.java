package com.caidaocloud.vms.application.dto.base;

import java.util.Map;

import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.JobGradeRange;
import com.caidaocloud.masterdata.dto.EmpWorkInfoVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2025/10/15
 */
@Data
public class PostInfoDto {
	@ApiModelProperty("岗位ID")
	private String bid;

	@ApiModelProperty("岗位名称")
	private String name;

	@ApiModelProperty("岗位编码")
	private String code;

	@ApiModelProperty("所属组织ID")
	private String orgId;

	@ApiModelProperty("所属组织名称")
	private String orgName;

	@ApiModelProperty("工作地ID")
	private String workplaceId;

	@ApiModelProperty("工作地名称")
	private String workplaceName;

	@ApiModelProperty("是否关键岗位")
	private Boolean keyPost;

	@ApiModelProperty("职级关联")
	private JobGradeRange jobGrade;

	@ApiModelProperty("关联职务或基准岗位，0：未配置，1：职务，2：基准岗位")
	private Integer relation;

	@ApiModelProperty("关联基准岗位ID")
	private String benchmarkPositionId;

	@ApiModelProperty("关联基准岗位名称")
	private String benchmarkPositionName;

	@ApiModelProperty("关联的职务ID")
	private String jobId;

	@ApiModelProperty("关联的职务名称")
	private String jobName;

	private Attachment jobDescFiles;

}
