package com.caidaocloud.vms.infrastructure.feign;

import java.util.List;

import com.caidaocloud.vms.application.dto.base.WfTaskRevokeDTO;
import com.caidaocloud.web.Result;

import org.springframework.stereotype.Component;

@Component
public class WfOperateFeignFallBack implements IWfOperateFeignClient {

    @Override
    public Result<Boolean> checkDefEnabled(String funCode) {
        return Result.fail();
    }

    @Override
    public Result<String> revokeProcessOfTask(WfTaskRevokeDTO wfTaskRevokeDTO) {
        return Result.fail();
    }

}
