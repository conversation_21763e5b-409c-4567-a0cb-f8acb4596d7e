# Repository AOP工作流实现方案

## 📋 概述

本文档描述了基于AOP（面向切面编程）的Repository层工作流实现方案。该方案通过统一拦截Repository层的save和update方法，实现了多个实体类型的工作流处理，完全解耦了Repository层和工作流逻辑。

## 🎯 设计目标

1. **完全解耦**：Repository层专注数据持久化，工作流逻辑由AOP切面统一处理
2. **统一拦截**：通过注解标记需要工作流处理的Repository方法
3. **多实体支持**：支持Project、ProjectPosition、ProjectContact、ProjectSupplier等多个实体类型
4. **透明处理**：Service层只需初始化WorkflowContext，无需关心工作流细节
5. **智能路由**：根据工作流配置自动选择直接保存或工作流处理

## 🏗️ 架构设计

### 核心组件

#### 1. @RepositoryWorkflow注解
```java
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RepositoryWorkflow {
    Class<?>[] entityTypes() default {};
    HistoryType defaultHistoryType() default HistoryType.BASIC_INFO;
    boolean enabled() default true;
    String description() default "";
}
```

#### 2. RepositoryWorkflowAspect切面
- 拦截标记了@RepositoryWorkflow的Repository方法
- 根据WorkflowContext判断是否需要工作流
- 处理工作流流程或直接执行原方法

#### 3. WorkflowContext上下文
- 线程安全的工作流上下文管理
- 存储实体类型、操作类型、历史记录类型等信息

#### 4. WorkflowService工作流服务
- 扩展支持多个实体类型的工作流判断
- 提供统一的工作流处理接口

## 🔧 实现细节

### Service层使用方式

```java
@PaasTransactional
public void savePosition(ProjectPositionCreateDto dto) {
    // 初始化工作流上下文
    WorkflowContext.WorkflowInfo workflowInfo = new WorkflowContext.WorkflowInfo(
            dto.getProjectId(), HistoryType.POSITION, ProjectPosition.class, 
            true, "新增项目岗位");
    WorkflowContext.setWorkflowInfo(workflowInfo);
    
    try {
        // 业务逻辑
        Project project = projectRepository.getById(dto.getProjectId());
        ProjectPosition position = project.createPosition(dto.getPosition(), dto.getCompany(), dto.getOrganization());
        
        // 保存岗位（会被Repository工作流切面拦截）
        projectPositionRepository.saveOrUpdate(position);
    } finally {
        // 清理工作流上下文
        WorkflowContext.clear();
    }
}
```

### Repository层注解标记

```java
@Override
@RepositoryWorkflow(entityTypes = {ProjectPosition.class}, description = "项目岗位保存或更新")
public String saveOrUpdate(ProjectPosition projectPosition) {
    // 原有的数据持久化逻辑
    if (projectPosition.getBid() == null) {
        DataInsert.identifier(ProjectPosition.identifier).insert(projectPosition);
        // ...
    } else {
        DataUpdate.identifier(ProjectPosition.identifier).update(projectPosition);
    }
    return projectPosition.getBid();
}
```

### AOP切面处理流程

1. **拦截方法调用**：检测到@RepositoryWorkflow注解
2. **获取工作流上下文**：从WorkflowContext获取当前操作信息
3. **实体类型验证**：检查实体类型是否支持工作流
4. **工作流判断**：调用WorkflowService判断是否需要工作流
5. **智能路由**：
   - 需要工作流：创建历史记录，启动工作流，返回临时ID
   - 不需要工作流：直接执行原Repository方法

## 📁 文件结构

### 新增文件
```
src/main/java/com/caidaocloud/vms/domain/project/
├── annotation/
│   └── RepositoryWorkflow.java                    # Repository工作流注解
├── aspect/
│   └── RepositoryWorkflowAspect.java              # Repository工作流AOP切面
└── context/
    └── WorkflowContext.java                       # 工作流上下文管理
```

### 修改文件
```
src/main/java/com/caidaocloud/vms/
├── application/service/
│   └── ProjectPositionService.java                # 移除@WorkflowEnabled，改用WorkflowContext
├── domain/project/service/
│   └── WorkflowService.java                       # 扩展支持多实体类型
├── domain/project/factory/
│   └── ProjectHistoryFactory.java                 # 添加create(String, HistoryType)方法
└── infrastructure/repository/
    ├── ProjectRepositoryImpl.java                 # 添加@RepositoryWorkflow注解
    ├── ProjectPositionRepositoryImpl.java         # 添加@RepositoryWorkflow注解
    ├── ProjectContactRepositoryImpl.java          # 添加@RepositoryWorkflow注解
    └── ProjectSupplierRepositoryImpl.java         # 添加@RepositoryWorkflow注解
```

## 🧪 测试验证

### 测试用例
1. **testRepositoryAopWorkflow_ProjectContact**：验证项目联系人的Repository AOP工作流
2. **testRepositoryAopWorkflow_NoContext**：验证无工作流上下文时直接保存
3. **原有测试用例**：验证岗位工作流的正确性

### 运行测试
```bash
mvn test -Dtest=WorkflowIntegrationTest
```

## 🚀 使用指南

### 1. 为新实体添加工作流支持

#### 步骤1：Repository添加注解
```java
@Override
@RepositoryWorkflow(entityTypes = {YourEntity.class}, description = "实体保存或更新")
public String saveOrUpdate(YourEntity entity) {
    // 原有逻辑
}
```

#### 步骤2：WorkflowService添加判断方法
```java
public boolean handleYourEntityWorkflow(String projectId, boolean isCreate) {
    // 实现工作流判断逻辑
    return true; // true=直接保存，false=需要工作流
}
```

#### 步骤3：RepositoryWorkflowAspect添加支持
```java
private boolean isWorkflowNeeded(Object entity, String projectId, WorkflowContext.WorkflowInfo workflowInfo) {
    // 添加新实体类型的判断
    if (entity instanceof YourEntity) {
        return !workflowService.handleYourEntityWorkflow(projectId, workflowInfo.isCreate());
    }
    // ...
}
```

#### 步骤4：Service层使用
```java
@PaasTransactional
public void saveYourEntity(YourEntityDto dto) {
    WorkflowContext.WorkflowInfo workflowInfo = new WorkflowContext.WorkflowInfo(
            dto.getProjectId(), HistoryType.YOUR_TYPE, YourEntity.class, 
            true, "新增实体");
    WorkflowContext.setWorkflowInfo(workflowInfo);
    
    try {
        // 业务逻辑
        yourEntityRepository.saveOrUpdate(entity);
    } finally {
        WorkflowContext.clear();
    }
}
```

### 2. 配置工作流规则

通过ProjectSetting配置各种工作流开关：
```java
ProjectSetting setting = new ProjectSetting();
setting.setPositionApprovalFlow(true);  // 岗位审批工作流
// 可以添加更多工作流配置字段
```

## 🎨 设计优势

### 1. 关注点分离
- **Repository层**：专注数据持久化，代码简洁
- **Service层**：专注业务逻辑，只需设置上下文
- **AOP切面**：统一处理工作流逻辑，易于维护

### 2. 可扩展性
- 新增实体类型只需添加注解和判断逻辑
- 工作流规则可以灵活配置
- 支持不同实体类型的不同工作流策略

### 3. 可维护性
- 工作流逻辑集中在切面中，便于统一修改
- 清晰的分层架构，职责明确
- 完善的测试覆盖，保证质量

### 4. 性能优化
- 线程安全的上下文管理
- 只在需要时进行工作流判断
- 避免不必要的数据库操作

## 📝 注意事项

1. **上下文管理**：Service层必须在finally块中清理WorkflowContext
2. **实体ID处理**：工作流场景下会生成临时ID，需要注意ID的使用
3. **事务管理**：确保工作流处理和数据保存在同一事务中
4. **异常处理**：切面中的异常需要正确传播到Service层
5. **测试环境**：确保测试环境有正确的Spring AOP配置

## 🔮 未来扩展

1. **工作流引擎集成**：可以集成更复杂的工作流引擎
2. **审批流程可视化**：提供工作流状态的可视化界面
3. **批量操作支持**：支持批量数据的工作流处理
4. **工作流模板**：提供可配置的工作流模板
5. **性能监控**：添加工作流处理的性能监控

---

这个Repository AOP工作流方案提供了一个优雅、可扩展的解决方案，完全实现了"不改动上层service代码"的同时，提供了强大的工作流处理能力。通过AOP的横切关注点分离，使得系统架构更加清晰和可维护。
