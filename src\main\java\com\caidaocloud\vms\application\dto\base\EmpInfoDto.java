package com.caidaocloud.vms.application.dto.base;

import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.JobGradeRange;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2025/10/15
 */
@Data
public class EmpInfoDto {
	@ApiModelProperty("业务ID")
	private String bid;
	@ApiModelProperty("员工ID")
	private String empId;
	@ApiModelProperty("员工工号")
	private String workno;
	@ApiModelProperty("员工姓名")
	private String name;
	@ApiModelProperty("员工英文名")
	private String enName;
	@ApiModelProperty("入职日期")
	private Long hireDate;
	@ApiModelProperty("员工状态")
	private EnumSimple empStatus;
	@ApiModelProperty("员工头像")
	private Attachment photo;
	@ApiModelProperty("直接上级")
	private EmpSimple leadEmpId;
	@ApiModelProperty("所属组织Id")
	private String organize;
	@ApiModelProperty("所属组织")
	private String organizeTxt;
	@ApiModelProperty("职级职等")
	private JobGradeRange jobGrade;
	@ApiModelProperty("关联的职务ID")
	private String job;
	@ApiModelProperty("岗位ID")
	private String post;
	@ApiModelProperty("岗位")
	private String postTxt;
}
