


msg:
 middleware:
  type: rabbitmq

rabbitmq:
 topics:
  - topic: PORJECT_POSITION
    exchange: caidao.vms
    routingKey: caidao.vms.position.change
    queue: caidao.vms.project.budget.refersh
    exchangeType: DIRECT
    tenantIsolated: false
    consumersCount: 1
  - topic: POSITION_SUPPLIER
    exchange: caidao.vms
    routingKey: caidao.vms.position.supplier
    queue: caidao.vms.project.position
    exchangeType: DIRECT
    tenantIsolated: false
    consumersCount: 1
spring:
  profiles:
    active: dev
