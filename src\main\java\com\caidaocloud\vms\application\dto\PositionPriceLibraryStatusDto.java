package com.caidaocloud.vms.application.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 岗位价格库状态操作DTO
 *
 * <AUTHOR>
 * @date 2025/12/10
 */
@Data
@ApiModel(description = "岗位价格库状态操作DTO")
public class PositionPriceLibraryStatusDto {

    @ApiModelProperty(value = "主键ID", required = true)
    @NotBlank(message = "主键ID不能为空")
    private String bid;
}
