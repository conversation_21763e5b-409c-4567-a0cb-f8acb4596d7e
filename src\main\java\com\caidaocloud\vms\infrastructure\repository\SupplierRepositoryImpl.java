package com.caidaocloud.vms.infrastructure.repository;

import java.util.ArrayList;
import java.util.List;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.vms.application.dto.SupplierQueryDTO;
import com.caidaocloud.vms.domain.supplier.entity.Supplier;
import com.caidaocloud.vms.domain.supplier.entity.SupplierTaxInfo;
import com.caidaocloud.vms.domain.supplier.repository.SupplierRepository;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import org.springframework.stereotype.Repository;

@Repository
public class SupplierRepositoryImpl implements SupplierRepository {

	@Override
	public String saveOrUpdate(Supplier supplier) {
		if (supplier.getBid() == null) {
			DataInsert.identifier(Supplier.identifier).insert(supplier);
			supplier.getTaxInfo().setBid(supplier.getBid());
			supplier.getTaxInfo().setSupplierId(supplier.getBid());
			DataInsert.identifier(SupplierTaxInfo.identifier).insert(supplier.getTaxInfo());
		}
		else {
			DataUpdate.identifier(Supplier.identifier).update(supplier);
		}
		return supplier.getBid();
	}

	@Override
	public Supplier getById(String supplierId) {
		Supplier supplier = DataQuery.identifier(Supplier.identifier).oneOrNull(supplierId, Supplier.class);
		if (supplier!=null)
			supplier.setTaxInfo(DataQuery.identifier(SupplierTaxInfo.identifier)
					.oneOrNull(supplierId, SupplierTaxInfo.class));
		return supplier;
	}

	@Override
	public PageResult<Supplier> findByPage(String supplierName, SupplierQueryDTO queryDTO) {
		return  DataQuery.identifier(Supplier.identifier)
				.limit(queryDTO.getPageSize(), queryDTO.getPageNo())
				.filter(DataFilter.ne("deleted", Boolean.TRUE.toString())
						.andRegexIf("supplierName", supplierName, () -> StringUtils.isNotEmpty(supplierName)), Supplier.class);
	}

	@Override
	public void delete(Supplier supplier) {
		DataDelete.identifier(Supplier.identifier).softDelete(supplier.getBid());
	}

	@Override
	public String saveOrUpdateTaxInfo(SupplierTaxInfo taxInfo) {
		if (taxInfo.getBid() == null) {
			taxInfo.setBid(taxInfo.getSupplierId());
			DataInsert.identifier(SupplierTaxInfo.identifier).insert(taxInfo);
		}
		else {
			DataUpdate.identifier(SupplierTaxInfo.identifier).update(taxInfo);
		}
		return taxInfo.getBid();
	}

	@Override
	public List<Supplier> list(List<String> idList) {
		if (CollectionUtils.isEmpty(idList)) {
			return new ArrayList<>();
		}
		return DataQuery.identifier(Supplier.identifier)
				.limit(-1, 1)
				.filter(DataFilter.ne("deleted", Boolean.TRUE.toString())
						.andIn("bid", idList), Supplier.class).getItems();

	}

	@Override
	public List<Supplier> list() {
		return DataQuery.identifier(Supplier.identifier)
				.limit(-1, 1)
				.filter(DataFilter.ne("deleted", Boolean.TRUE.toString()), Supplier.class).getItems();
	}
}
