package com.caidaocloud.vms.infrastructure.repository;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.vms.domain.employee.entity.OnboardingEmployee;
import com.caidaocloud.vms.domain.employee.entity.onboarding.*;
import com.caidaocloud.vms.domain.employee.repository.OnboardingRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 预入职Repository实现类
 *
 * <AUTHOR> Zhou
 * @date 2025/10/30
 */
@Repository
@Slf4j
public class OnboardingRepositoryImpl implements OnboardingRepository {

    @Override
    public String saveOrUpdate(OnboardingEmployee onboardingEmployee) {
        String empId = saveOrUpdateWorkInfo(onboardingEmployee.getOnboardingEmpWorkInfo());
        saveOrUpdatePrivateInfo(onboardingEmployee.getOnboardingEmpPrivateInfo().setEmpId(empId));
        saveOrUpdateContract(onboardingEmployee.getOnboardingContract().setEmpId(empId));
        saveOrUpdateOtherContract(onboardingEmployee.getOnboardingOtherContract().setEmpId(empId));
        saveOrUpdateSalary(onboardingEmployee.getOnboardingSalary().setEmpId(empId));
        return empId;
    }

    @Override
    public OnboardingEmployee detail(String empId) {
        Optional<OnboardingEmpWorkInfo> empWorkInfo = getWorkInfoByEmpId(empId);
        Optional<OnboardingEmpPrivateInfo> empPrivateInfo = getPrivateInfoByEmpId(empId);
        Optional<OnboardingContract> contract = getContractByEmpId(empId);
        Optional<OnboardingOtherContract> otherContract = getOtherContractByEmpId(empId);
        Optional<OnboardingSalary> salary = getSalaryByEmpId(empId);
        if (!empWorkInfo.isPresent() || !empPrivateInfo.isPresent()) {
            throw new ServerException("员工不存在");
        }
        return new OnboardingEmployee(empWorkInfo.get(), empPrivateInfo.get(), contract.orElse(null),
                otherContract.orElse(null), salary.orElse(null));
    }

    public String saveOrUpdateWorkInfo(OnboardingEmpWorkInfo workInfo) {
        if (workInfo.getBid() == null) {
            workInfo.setEmpId(SnowUtil.nextId());
            DataInsert.identifier(OnboardingEmpWorkInfo.identifier).insert(workInfo);
            log.info("保存员工工作信息成功，ID: {}", workInfo.getBid());
        } else {
            DataUpdate.identifier(OnboardingEmpWorkInfo.identifier).update(workInfo);
            log.info("更新员工工作信息成功，ID: {}", workInfo.getBid());
        }
        return workInfo.getEmpId();
    }

    public String saveOrUpdatePrivateInfo(OnboardingEmpPrivateInfo privateInfo) {
        if (privateInfo.getBid() == null) {
            DataInsert.identifier(OnboardingEmpPrivateInfo.identifier).insert(privateInfo);
            log.info("保存员工个人信息成功，ID: {}", privateInfo.getBid());
        } else {
            DataUpdate.identifier(OnboardingEmpPrivateInfo.identifier).update(privateInfo);
            log.info("更新员工个人信息成功，ID: {}", privateInfo.getBid());
        }
        return privateInfo.getBid();
    }

    public String saveOrUpdateContract(OnboardingContract contract) {
        if (contract.getBid() == null) {
            DataInsert.identifier(OnboardingContract.identifier).insert(contract);
            log.info("保存合同信息成功，ID: {}", contract.getBid());
        } else {
            DataUpdate.identifier(OnboardingContract.identifier).update(contract);
            log.info("更新合同信息成功，ID: {}", contract.getBid());
        }
        return contract.getBid();
    }

    public String saveOrUpdateSalary(OnboardingSalary salary) {
        if (salary.getBid() == null) {
            DataInsert.identifier(OnboardingSalary.identifier).insert(salary);
            log.info("保存薪资信息成功，ID: {}", salary.getBid());
        } else {
            DataUpdate.identifier(OnboardingSalary.identifier).update(salary);
            log.info("更新薪资信息成功，ID: {}", salary.getBid());
        }
        return salary.getBid();
    }

    public String saveOrUpdateOtherContract(OnboardingOtherContract otherContract) {
        if (otherContract.getBid() == null) {
            DataInsert.identifier(OnboardingOtherContract.identifier).insert(otherContract);
            log.info("保存其他合同信息成功，ID: {}", otherContract.getBid());
        } else {
            DataUpdate.identifier(OnboardingOtherContract.identifier).update(otherContract);
            log.info("更新其他合同信息成功，ID: {}", otherContract.getBid());
        }
        return otherContract.getBid();
    }

    public String saveOrUpdateEduExp(OnboardingEmpEduExp eduExp) {
        if (eduExp.getBid() == null) {
            DataInsert.identifier(OnboardingEmpEduExp.identifier).insert(eduExp);
            log.info("保存教育经历成功，ID: {}", eduExp.getBid());
        } else {
            DataUpdate.identifier(OnboardingEmpEduExp.identifier).update(eduExp);
            log.info("更新教育经历成功，ID: {}", eduExp.getBid());
        }
        return eduExp.getBid();
    }

    public List<String> batchSaveEduExp(List<OnboardingEmpEduExp> eduExpList) {
        List<String> resultIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(eduExpList)) {
            for (OnboardingEmpEduExp eduExp : eduExpList) {
                String id = saveOrUpdateEduExp(eduExp);
                resultIds.add(id);
            }
            log.info("批量保存教育经历成功，共{}条记录", eduExpList.size());
        }
        return resultIds;
    }

    public Optional<OnboardingEmpWorkInfo> getWorkInfoByEmpId(String empId) {
        PageResult<OnboardingEmpWorkInfo> result = DataQuery.identifier(OnboardingEmpWorkInfo.identifier)
                .filter(DataFilter.eq("empId", empId)
                        .andNe("deleted", Boolean.TRUE.toString()), OnboardingEmpWorkInfo.class);
        return result.getItems().isEmpty() ? Optional.empty() : Optional.of(result.getItems().get(0));

    }

    public Optional<OnboardingEmpPrivateInfo> getPrivateInfoByEmpId(String empId) {
        PageResult<OnboardingEmpPrivateInfo> result = DataQuery.identifier(OnboardingEmpPrivateInfo.identifier)
                .filter(DataFilter.eq("empId", empId)
                        .andNe("deleted", Boolean.TRUE.toString()), OnboardingEmpPrivateInfo.class);
        return result.getItems().isEmpty() ? Optional.empty() : Optional.of(result.getItems().get(0));
    }

    public Optional<OnboardingContract> getContractByEmpId(String empId) {
        PageResult<OnboardingContract> result = DataQuery.identifier(OnboardingContract.identifier)
                .filter(DataFilter.eq("empId", empId)
                        .andNe("deleted", Boolean.TRUE.toString()), OnboardingContract.class);
        return result.getItems().isEmpty() ? Optional.empty() : Optional.of(result.getItems().get(0));
    }

    public Optional<OnboardingSalary> getSalaryByEmpId(String empId) {
        PageResult<OnboardingSalary> result = DataQuery.identifier(OnboardingSalary.identifier)
                .filter(DataFilter.eq("empId", empId)
                        .andNe("deleted", Boolean.TRUE.toString()), OnboardingSalary.class);
        return result.getItems().isEmpty() ? Optional.empty() : Optional.of(result.getItems().get(0));
    }

    public Optional<OnboardingOtherContract> getOtherContractByEmpId(String empId) {
        PageResult<OnboardingOtherContract> result = DataQuery.identifier(OnboardingOtherContract.identifier)
                .filter(DataFilter.eq("empId", empId)
                        .andNe("deleted", Boolean.TRUE.toString()), OnboardingOtherContract.class);
        return result.getItems().isEmpty() ? Optional.empty() : Optional.of(result.getItems().get(0));
    }

    public List<OnboardingEmpEduExp> getEduExpListByEmpId(String empId) {
        return DataQuery.identifier(OnboardingEmpEduExp.identifier)
                .limit(-1, 1)
                .filter(DataFilter.eq("empId", empId)
                        .andNe("deleted", Boolean.TRUE.toString()), OnboardingEmpEduExp.class)
                .getItems();
    }

    @Override
    public void deleteAllOnboardingDataByEmpId(String empId) {
        log.info("开始删除员工预入职数据，员工ID: {}", empId);

        try {
            // 1. 删除员工工作信息
            deleteWorkInfoByEmpId(empId);

            // 2. 删除员工个人信息
            deletePrivateInfoByEmpId(empId);

            // 3. 删除合同信息
            deleteContractByEmpId(empId);

            // 4. 删除薪资信息
            deleteSalaryByEmpId(empId);

            // 5. 删除其他合同信息
            deleteOtherContractByEmpId(empId);

            // 6. 删除教育经历
            deleteEduExpByEmpId(empId);

            log.info("员工预入职数据删除完成，员工ID: {}", empId);
        } catch (Exception e) {
            log.error("删除员工预入职数据失败，员工ID: {}", empId, e);
            throw new RuntimeException("删除员工预入职数据失败", e);
        }
    }

    private void deleteWorkInfoByEmpId(String empId) {
        List<OnboardingEmpWorkInfo> workInfoList = DataQuery.identifier(OnboardingEmpWorkInfo.identifier)
                .filter(DataFilter.eq("empId", empId)
                        .andNe("deleted", Boolean.TRUE.toString()), OnboardingEmpWorkInfo.class)
                .getItems();

        for (OnboardingEmpWorkInfo workInfo : workInfoList) {
            DataDelete.identifier(OnboardingEmpWorkInfo.identifier).softDelete(workInfo.getBid());
            log.debug("删除员工工作信息，ID: {}", workInfo.getBid());
        }
    }

    private void deletePrivateInfoByEmpId(String empId) {
        List<OnboardingEmpPrivateInfo> privateInfoList = DataQuery.identifier(OnboardingEmpPrivateInfo.identifier)
                .filter(DataFilter.eq("empId", empId)
                        .andNe("deleted", Boolean.TRUE.toString()), OnboardingEmpPrivateInfo.class)
                .getItems();

        for (OnboardingEmpPrivateInfo privateInfo : privateInfoList) {
            DataDelete.identifier(OnboardingEmpPrivateInfo.identifier).softDelete(privateInfo.getBid());
            log.debug("删除员工个人信息，ID: {}", privateInfo.getBid());
        }
    }

    private void deleteContractByEmpId(String empId) {
        List<OnboardingContract> contractList = DataQuery.identifier(OnboardingContract.identifier)
                .filter(DataFilter.eq("empId", empId)
                        .andNe("deleted", Boolean.TRUE.toString()), OnboardingContract.class)
                .getItems();

        for (OnboardingContract contract : contractList) {
            DataDelete.identifier(OnboardingContract.identifier).softDelete(contract.getBid());
            log.debug("删除合同信息，ID: {}", contract.getBid());
        }
    }

    private void deleteSalaryByEmpId(String empId) {
        List<OnboardingSalary> salaryList = DataQuery.identifier(OnboardingSalary.identifier)
                .filter(DataFilter.eq("empId", empId)
                        .andNe("deleted", Boolean.TRUE.toString()), OnboardingSalary.class)
                .getItems();

        for (OnboardingSalary salary : salaryList) {
            DataDelete.identifier(OnboardingSalary.identifier).softDelete(salary.getBid());
            log.debug("删除薪资信息，ID: {}", salary.getBid());
        }
    }

    private void deleteOtherContractByEmpId(String empId) {
        List<OnboardingOtherContract> otherContractList = DataQuery.identifier(OnboardingOtherContract.identifier)
                .filter(DataFilter.eq("empId", empId)
                        .andNe("deleted", Boolean.TRUE.toString()), OnboardingOtherContract.class)
                .getItems();

        for (OnboardingOtherContract otherContract : otherContractList) {
            DataDelete.identifier(OnboardingOtherContract.identifier).softDelete(otherContract.getBid());
            log.debug("删除其他合同信息，ID: {}", otherContract.getBid());
        }
    }

    private void deleteEduExpByEmpId(String empId) {
        List<OnboardingEmpEduExp> eduExpList = DataQuery.identifier(OnboardingEmpEduExp.identifier)
                .filter(DataFilter.eq("empId", empId)
                        .andNe("deleted", Boolean.TRUE.toString()), OnboardingEmpEduExp.class)
                .getItems();

        for (OnboardingEmpEduExp eduExp : eduExpList) {
            DataDelete.identifier(OnboardingEmpEduExp.identifier).softDelete(eduExp.getBid());
            log.debug("删除教育经历，ID: {}", eduExp.getBid());
        }
    }
}
