package com.caidaocloud.vms.application.vo;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.vms.domain.supplier.enums.SupplierStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
@ApiModel(description = "供应商分页信息 VO")
@Data
public class SupplierPageVO {

    @ApiModelProperty(value = "业务ID")
    private String bid;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "员工规模")
    private DictSimple staffSize;

    @ApiModelProperty(value = "收入规模")
    private DictSimple revenueScale;

    @ApiModelProperty(value = "擅长行业")
    private List<DictSimple> industry;

    @ApiModelProperty(value = "合同有效日期")
    private Long endDate;

    @ApiModelProperty(value = "已合作年份")
    private BigDecimal year;

    @ApiModelProperty(value = "合作状态")
    private SupplierStatus status;
}