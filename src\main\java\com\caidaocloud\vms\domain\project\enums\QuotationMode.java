package com.caidaocloud.vms.domain.project.enums;

import java.math.BigDecimal;
import java.math.RoundingMode;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import org.apache.commons.lang3.StringUtils;

public enum QuotationMode {
    PACKAGED(0, "打包收费"){
        @Override
        public String calcValue(String salary,String quotationValue) {
            return "";
        }
    },
    FIXED(1, "固定收费"){
        @Override
        public String calcValue(String salary,String quotationValue) {
            return quotationValue;
        }
    },
    PERCENTAGE(2, "按百分比收费"){
        @Override
        public String calcValue(String salary,String quotationValue) {
            if (StringUtils.isEmpty(quotationValue) || StringUtils.isEmpty(salary)) {
                return "0";
            }
            BigDecimal percent = new BigDecimal(quotationValue);
            BigDecimal value = new BigDecimal(salary);
            return value.multiply(percent).divide(BigDecimal.valueOf(100L),2, RoundingMode.HALF_UP).toString();
        }
    };

    private final int value;
    private final String display;

    QuotationMode(int value, String display) {
        this.value = value;
        this.display = display;
    }

    public int getValue() {
        return value;
    }

    public String getDisplay() {
        return display;
    }

    public static QuotationMode fromValue(int value) {
        for (QuotationMode mode : QuotationMode.values()) {
            if (mode.value == value) {
                return mode;
            }
        }
        throw new IllegalArgumentException("Unknown value: " + value);
    }

    public static QuotationMode fromValue(EnumSimple value) {
        if (value == null || StringUtils.isEmpty(value.getValue())) {
            return null;
        }
        int v = Integer.parseInt(value.getValue());
        for (QuotationMode status : QuotationMode.values()) {
            if (status.value == v) {
                return status;
            }
        }
        return null;
    }
    public EnumSimple toEnumSimple() {
        EnumSimple enumSimple = new EnumSimple();
        enumSimple.setValue(String.valueOf(value));
        return enumSimple;
    }

    public abstract String calcValue(String salary,String quotationValue);

}