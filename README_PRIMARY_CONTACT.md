# 供应商主要联系人功能实现

## 功能概述

新增了两个接口来管理供应商的主要联系人：

1. **设置主要联系人** - 将指定供应商联系人设置为主要联系人，一个供应商只有一个主要联系人
2. **取消主要联系人** - 取消联系人的主要联系人状态

## 实现的文件修改

### 1. 实体类修改

#### SupplierContact.java
- 新增 `setPrimary()` 方法：设置为主要联系人
- 新增 `unsetPrimary()` 方法：取消主要联系人
- 修改 `update()` 方法：支持 isPrimary 字段更新

### 2. Repository层修改

#### SupplierContactRepository.java
- 新增 `findPrimaryContactBySupplier(String supplierId)` 方法：查找指定供应商的主要联系人

#### SupplierContactRepositoryImpl.java
- 实现 `findPrimaryContactBySupplier()` 方法

### 3. Service层修改

#### SupplierService.java
- 新增 `setPrimaryContact(String contactId)` 方法：设置主要联系人
- 新增 `unsetPrimaryContact(String contactId)` 方法：取消主要联系人

### 4. Controller层修改

#### SupplierController.java
- 新增 `/contact/setPrimary` 接口：设置主要联系人
- 新增 `/contact/unsetPrimary` 接口：取消主要联系人

### 5. DTO/VO修改

#### SupplierContactDto.java
- 新增 `isPrimary` 字段

#### SupplierContactVO.java
- 新增 `isPrimary` 字段

## API接口说明

### 设置主要联系人
```
POST /api/vms/v1/manager/supplier/contact/setPrimary
参数: contactId (联系人ID)
```

### 取消主要联系人
```
POST /api/vms/v1/manager/supplier/contact/unsetPrimary
参数: contactId (联系人ID)
```

## 业务逻辑

1. **设置主要联系人时**：
   - 检查联系人是否存在
   - 查找当前供应商的主要联系人
   - 如果存在其他主要联系人，先将其设置为非主要联系人
   - 将指定联系人设置为主要联系人

2. **取消主要联系人时**：
   - 检查联系人是否存在
   - 将联系人的主要联系人状态设置为false

## 数据库字段

SupplierContact表中的 `isPrimary` 字段用于标识是否为主要联系人。

## 完整的业务流程

### 创建联系人时
- 如果在创建时设置 `isPrimary=true`，系统会自动检查并取消当前供应商的其他主要联系人
- 支持在DTO中传递 `isPrimary` 字段

### 编辑联系人时
- 如果将联系人从非主要联系人改为主要联系人，系统会自动处理冲突
- 支持通过编辑接口修改主要联系人状态

### 专用接口
- 提供专门的设置/取消主要联系人接口，操作更明确
- 接口会进行完整的业务逻辑校验

## 技术实现要点

1. **事务管理**：所有涉及主要联系人变更的操作都使用 `@PaasTransactional` 确保数据一致性
2. **业务校验**：在设置新的主要联系人前，会先查询并取消现有的主要联系人
3. **Repository扩展**：新增了 `findPrimaryContactBySupplier` 方法用于查询主要联系人
4. **实体方法**：在 `SupplierContact` 实体中添加了 `setPrimary()` 和 `unsetPrimary()` 方法

## 注意事项

- 一个供应商同时只能有一个主要联系人
- 系统会自动处理主要联系人的唯一性约束
- 所有相关操作都会更新 `updateTime` 和 `updateBy` 字段
