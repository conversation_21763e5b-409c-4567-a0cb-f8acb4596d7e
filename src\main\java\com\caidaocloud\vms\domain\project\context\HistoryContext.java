package com.caidaocloud.vms.domain.project.context;

import com.caidaocloud.vms.domain.project.entity.ProjectHistory;
import com.caidaocloud.vms.domain.project.entity.ProjectHistoryDetail;
import com.caidaocloud.vms.domain.project.enums.HistoryType;
import com.caidaocloud.vms.domain.project.enums.OperationType;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 工作流上下文
 * 用于在AOP切面中传递工作流相关信息
 * 
 * <AUTHOR>
 * @date 2025/9/25
 */
public class HistoryContext {

    private static final ThreadLocal<HistoryContextInfo> CONTEXT = new ThreadLocal<>();

    /**
     * 设置工作流信息
     * @param historyContextInfo 工作流信息
     */
    public static void setHistoryContextInfo(HistoryContextInfo historyContextInfo) {
        CONTEXT.set(historyContextInfo);
    }

    /**
     * 获取工作流信息
     * @return 工作流信息
     */
    public static HistoryContextInfo getHistoryContextInfo() {
        return CONTEXT.get();
    }

    public static  boolean isEmpty(){
        return CONTEXT.get() == null;
    }

    /**
     * 清除工作流信息
     */
    public static void clear() {
        CONTEXT.remove();
    }

    /**
     * 工作流信息
     */
    @Data
    public static class HistoryContextInfo {
        private String projectId;
        private String positionId;
        private HistoryType historyType;
        private Class<?> entityType;
        private OperationType operationType;
        private Object entity;
        private boolean workflowEnabled;
        private boolean postWorkflowEnabled;

        public HistoryContextInfo(String projectId, HistoryType historyType, Class<?> entityType, OperationType operationType, boolean workflowEnabled, boolean postWorkflowEnabled) {
            this.projectId = projectId;
            this.historyType = historyType;
            this.entityType = entityType;
            this.operationType = operationType;
            this.workflowEnabled = workflowEnabled;
            this.postWorkflowEnabled = postWorkflowEnabled;
        }

    }
}
