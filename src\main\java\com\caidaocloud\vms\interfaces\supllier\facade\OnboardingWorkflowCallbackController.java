package com.caidaocloud.vms.interfaces.supllier.facade;

import com.caidaocloud.vms.application.service.OnboardingService;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfCallbackResultDto;
import com.caidaocloud.workflow.enums.WfCallbackTriggerOperationEnum;
import com.caidaocloud.vms.application.service.WorkflowCallbackService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 预入职工作流回调Controller
 * 处理预入职工作流审批结果的回调
 *
 * <AUTHOR>
 * @date 2025/10/31
 */
@RestController
@RequestMapping("/api/vms/v1/employee/onboarding")
@Api(tags = "预入职工作流回调管理")
@Slf4j
public class OnboardingWorkflowCallbackController {

    @Autowired
    private OnboardingService onboardingService;

    /**
     * 预入职工作流审批通过回调
     *
     * @param dto 业务单据ID（员工变更记录ID）
     * @return 操作结果
     */
    @PostMapping("/workflow/approve")
    @ApiOperation(value = "预入职工作流审批通过")
    public Result approveOnboardingWorkflow(
            @RequestBody WfCallbackResultDto dto) {

        log.info("预入职工作流审批通过回调，dto: {}", dto);
        onboardingService.callback(dto.getBusinessKey(), dto.getTenantId(),
                WfCallbackTriggerOperationEnum.APPROVED);
         return Result.ok();

    }

    /**
     * 预入职工作流审批拒绝回调
     *
     * @param dto 业务单据ID（员工变更记录ID）
     * @return 操作结果
     */
    @PostMapping("/workflow/reject")
    @ApiOperation(value = "预入职工作流审批拒绝")
    public Result rejectOnboardingWorkflow(
            @RequestBody WfCallbackResultDto dto) {

        log.info("预入职工作流审批拒绝回调，dto: {}", dto);
        onboardingService.callback(dto.getBusinessKey(), dto.getTenantId(),
                WfCallbackTriggerOperationEnum.REFUSED);
         return Result.ok();

    }
}
