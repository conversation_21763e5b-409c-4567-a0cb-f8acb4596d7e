package com.caidaocloud.vms.application.dto;

import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "供应商合同信息")
public class SupplierContractDto {
    @ApiModelProperty(value = "合同ID")
    private String bid;

    @ApiModelProperty(value = "供应商ID", required = true)
    private String supplierId;

    @ApiModelProperty(value = "合同名称", required = true)
    private String contractName;

    @ApiModelProperty(value = "合同编号", required = true)
    private String contractNumber;

    @ApiModelProperty(value = "生效日期(时间戳)", example = "1714521600000", required = true)
    private Long effectiveDate;

    @ApiModelProperty(value = "到期日期(时间戳)", example = "1746057600000", required = true)
    private Long expiryDate;

    @ApiModelProperty(value = "合同附件")
    private Attachment attachment;
}
