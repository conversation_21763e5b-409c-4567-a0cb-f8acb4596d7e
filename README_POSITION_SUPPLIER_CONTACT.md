# 岗位供应商联系人和提供人数编辑功能

## 功能概述

本次新增了岗位供应商联系人和提供人数的编辑功能，并实现了提供人数的校验机制，确保所有供应商的提供人数总和不超过岗位的计划人数。

## 新增接口

### 1. 编辑岗位供应商联系人和提供人数

**接口路径**: `POST /api/vms/v1/manager/project/position/supplier/editContact`

**功能描述**: 编辑岗位供应商的联系人和提供人数，会校验所有供应商提供人数总和不超过岗位计划人数

**请求参数**: `PositionSupplierDto`
```json
{
  "bid": "关系ID（必填）",
  "supplierContact": ["联系人ID列表"],
  "provide": "提供人数"
}
```

**响应**: 标准Result响应

## 业务逻辑

### 1. 校验机制

当编辑岗位供应商的提供人数时，系统会执行以下校验：

1. **获取岗位信息**: 根据岗位ID获取岗位的计划人数(plannedHeadcount)
2. **计算当前总提供人数**: 统计该岗位下所有供应商的提供人数总和（排除当前正在编辑的记录）
3. **加入新的提供人数**: 将新设置的提供人数加入总和
4. **校验**: 确保总提供人数不超过岗位计划人数

### 2. 错误处理

- 如果关系ID为空，抛出"关系ID不能为空"异常
- 如果岗位供应商关系不存在，抛出"岗位供应商关系不存在"异常
- 如果岗位不存在，抛出"岗位不存在"异常
- 如果岗位计划人数未设置或无效，抛出"岗位计划人数未设置或无效"异常
- 如果总提供人数超过计划人数，抛出详细的错误信息，包含当前总数和计划人数

## 技术实现

### 1. Service层方法

#### editPositionSupplierContact()
- 专门用于编辑联系人和提供人数
- 包含完整的校验逻辑
- 使用@PaasTransactional确保事务一致性

#### validateTotalProvideCount()
- 私有方法，用于校验提供人数总和
- 支持排除特定记录（用于更新场景）
- 提供详细的错误信息

### 2. 现有方法增强

#### addPositionSupplier()
- 在添加岗位供应商时也会进行提供人数校验

#### updatePositionSupplier()
- 在更新岗位供应商信息时进行提供人数校验

## 使用示例

### 编辑联系人和提供人数

```http
POST /api/vms/v1/manager/project/position/supplier/editContact
Content-Type: application/json

{
  "bid": "supplier-relation-123",
  "supplierContact": ["contact-001", "contact-002"],
  "provide": 5
}
```

### 成功响应

```json
{
  "code": 200,
  "message": "操作成功",
  "data": null
}
```

### 错误响应（超出计划人数）

```json
{
  "code": 500,
  "message": "所有供应商提供人数总和(15)不能超过岗位计划人数(10)",
  "data": null
}
```

## 注意事项

1. **数据一致性**: 所有涉及提供人数的操作都在事务中执行，确保数据一致性
2. **校验时机**: 在保存数据前进行校验，避免无效数据入库
3. **灵活性**: 支持只更新联系人、只更新提供人数，或同时更新两者
4. **向后兼容**: 现有的updatePositionSupplier接口保持不变，同时增加了校验功能

## 相关文件

- `ProjectPositionService.java`: 新增editPositionSupplierContact()和validateTotalProvideCount()方法
- `ProjectPositionController.java`: 新增/supplier/editContact接口
- `PositionSupplierDto.java`: 已包含supplierContact和provide字段
- `PositionSupplier.java`: 实体类已支持联系人和提供人数字段
