package com.caidaocloud.vms.domain.base.enums;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;

/**
 *
 * <AUTHOR>
 * @date 2025/11/4
 */
public enum ApprovalStatus {
	PENDING(0, "待审批"),
	APPROVED(1, "已通过"),
	REJECTED(2,"已拒绝"),
	CANCELLED(3,"已撤销"),
	;

	private final int code;
	private final String description;

	ApprovalStatus(int code, String description) {
		this.code = code;
		this.description = description;
	}

	public EnumSimple toEnumSimple() {
		EnumSimple enumSimple = new EnumSimple();
		enumSimple.setValue(String.valueOf(code));
		// enumSimple.setDesc(desc);
		return enumSimple;
	}

	public static ApprovalStatus fromCode(int code) {
		for (ApprovalStatus status : ApprovalStatus.values()) {
			if (status.getCode() == code) {
				return status;
			}
		}
		throw new ServerException("Unknown project status code: " + code);
	}

	public int getCode() {
		return code;
	}
}
