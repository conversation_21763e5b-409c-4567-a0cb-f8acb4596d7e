
package com.caidaocloud.vms.application.dto;

import java.math.BigDecimal;
import java.util.List;

import com.caidaocloud.vms.domain.project.enums.QuotationMode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel(description = "岗位供应商关系信息")
@NoArgsConstructor
public class PositionSupplierPublishDto {

    @ApiModelProperty(value = "关系ID集合")
    private List<String> bid;

    @ApiModelProperty(value = "岗位ID", required = true)
    private String positionId;

}
