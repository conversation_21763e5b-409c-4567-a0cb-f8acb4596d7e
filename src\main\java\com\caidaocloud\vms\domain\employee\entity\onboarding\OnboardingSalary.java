package com.caidaocloud.vms.domain.employee.entity.onboarding;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.vms.domain.base.entity.BaseEntity;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class OnboardingSalary extends BaseEntity {

    private String empId;

    private String salary;

    private EnumSimple quotationMode;

    private String quotationValue;

    public static String identifier = "entity.vms.OnboardingSalary";

    @Override
    public String getEntityIdentifier() {
        return identifier;
    }
}