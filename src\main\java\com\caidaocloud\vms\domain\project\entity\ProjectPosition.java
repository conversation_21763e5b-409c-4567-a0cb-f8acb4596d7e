package com.caidaocloud.vms.domain.project.entity;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.vms.application.dto.ProjectPositionEditDto;
import com.caidaocloud.vms.domain.project.annotation.DisplayName;
import com.caidaocloud.vms.domain.project.dto.FormatAllResult;
import com.caidaocloud.vms.domain.project.dto.FormatResult;
import com.caidaocloud.vms.domain.project.enums.OperationType;
import com.caidaocloud.vms.domain.project.enums.PositionStatus;
import com.caidaocloud.vms.domain.project.enums.ProjectStatus;
import com.caidaocloud.vms.domain.project.history.DetailedHistoryFormat;
import com.caidaocloud.vms.domain.project.history.SimplifiedHistoryFormat;
import com.googlecode.totallylazy.Sequences;
import lombok.AccessLevel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor(access = AccessLevel.PRIVATE)

public class ProjectPosition extends SimplifiedHistoryFormat<ProjectPosition> {

	private String projectId;

	@DisplayName("所属公司")
	private String company;

	@DisplayName("所属组织")
	private String organization;

	private String position;

	private String positionCode;

	@DisplayName("岗位")
	private String positionName;

	@DisplayName("开始日期")
	private Long startDate;

	@DisplayName("结束日期")
	private Long endDate;

	@DisplayName("周期类型")
	private EnumSimple durationType;

	@DisplayName("周期")
	private Integer duration;

	@DisplayName("预算总额")
	private Integer totalBudget;

	@DisplayName("已用预算")
	private Integer usedBudget;

	@DisplayName("计划人数")
	private Integer plannedHeadcount;

	@DisplayName("实际人数")
	private Integer actualHeadcount;

	@DisplayName("工作地")
	private String workplace;

	@DisplayName("联系人")
	private EmpSimple contact;

	@DisplayName("雇佣类型")
	private DictSimple employmentType;

	private PositionStatus status;
	private Boolean isEmergency;
	private String minSalary;
	private String maxSalary;

	private PositionRequirement positionRequirement;

	private List<PositionSupplier> positionSuppliers;

	public static String identifier = "entity.vms.ProjectPosition";

	@Override
	public String getEntityIdentifier() {
		return identifier;
	}

	public ProjectPosition(String projectId, String position, String company, String organization) {
		this.projectId = projectId;
		this.position = position;
		this.company = company;
		this.organization = organization;
		this.status = PositionStatus.UN_SUBMITTED;
		positionRequirement = new PositionRequirement();
	}

	public void updateBasicInfo(ProjectPositionEditDto positionDto) {
		startDate = positionDto.getStartDate();
		endDate = positionDto.getEndDate();
		durationType = positionDto.getDurationType().toEnumSimple();
		duration = positionDto.getDuration();
		totalBudget = positionDto.getTotalBudget();
		plannedHeadcount = positionDto.getPlannedHeadcount();
		company = positionDto.getCompany();
		workplace = positionDto.getWorkplace();
		contact = new EmpSimple();
		contact.setEmpId(positionDto.getContactEmpId());
		employmentType = new DictSimple();
		employmentType.setValue(positionDto.getEmploymentType());
		if (startDate > endDate) {
			throw new ServerException("开始时间必须早于结束时间");
		}
		isEmergency = positionDto.getIsEmergency();
		minSalary = positionDto.getMinSalary();
		maxSalary = positionDto.getMaxSalary();
		setUpdateTime(System.currentTimeMillis());
		setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
	}

	private DetailedHistoryFormat detailedHistoryFormat = new DetailedHistoryFormat<ProjectPosition>() {

		@Override
		public String getEntityIdentifier() {
			return identifier;
		}
	};

	public FormatAllResult formatAll(ProjectDraft positionDraft, List<ProjectDraft> detailDraftList) {
		List<ProjectChange> positionInfoChangeList = detailedHistoryFormat.format(this, positionDraft);
		List<String> draftIds = new ArrayList<>();
		ProjectPosition snapshot = FastjsonUtil.toObject(positionDraft.getSnapshot(), ProjectPosition.class);
		FormatResult<PositionRequirement> requirementResult = format(positionRequirement, detailDraftList,
				PositionRequirement.class);
		positionInfoChangeList.addAll(requirementResult.getChanges());
		draftIds.addAll(requirementResult.getDraftIdList());

		snapshot.setPositionRequirement(requirementResult.getEntity());
		List<PositionSupplier> supplierList = Sequences.sequence(positionSuppliers).map(data -> {
			FormatResult<PositionSupplier> supplierResult = format(data, detailDraftList, PositionSupplier.class);
			positionInfoChangeList.addAll(supplierResult.getChanges());
			draftIds.addAll(supplierResult.getDraftIdList());
			return supplierResult.getEntity();
		}).toList();
		snapshot.setPositionSuppliers(supplierList);
		return FormatAllResult.of(FastjsonUtil.toJson(snapshot), positionInfoChangeList, draftIds);
	}

	@Override
	public String formatDisplay() {
		return String.format("%s,%s", positionCode, positionName);
	}

	public void approve() {
		update();
		status = PositionStatus.APPROVED;
	}

	public void reject() {
		update();
		status = PositionStatus.REJECTED;
	}

	public void checkUpdate() {
		if (status == PositionStatus.UNDER_REVIEW || status == PositionStatus.PUBLISHED
				|| status == PositionStatus.CLOSED) {
			throw new ServerException("岗位不可编辑");
		}

	}

	public void publish() {
		update();
		status = PositionStatus.PUBLISHED;
	}

	public void close() {
		update();
		status = PositionStatus.CLOSED;
	}

	public void revoke() {
		update();
		status = PositionStatus.WITHDRAWN;
	}
}