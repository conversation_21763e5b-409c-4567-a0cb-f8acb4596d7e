package com.caidaocloud.vms.application.service.emp;

import java.util.List;

import com.caidaocloud.vms.application.dto.base.OrgInfoDto;
import com.caidaocloud.vms.domain.base.repository.OrganizeRepository;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 * @date 2025/10/15
 */
@Service
public class OrganizeService {
    @Autowired
    private OrganizeRepository organizeRepository;

    public List<OrgInfoDto> loadOrgList(List<String> orgIds) {
        return organizeRepository.loadOrgList(orgIds);
    }
}
