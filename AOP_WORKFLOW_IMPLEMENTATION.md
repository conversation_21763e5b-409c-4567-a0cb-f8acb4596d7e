# AOP工作流实现方案

## 概述

基于AOP（面向切面编程）的工作流实现方案，完全解耦了Repository层和工作流逻辑，实现了更加优雅和可维护的架构。

## 核心设计理念

### 1. 关注点分离
- **Repository层**：专注于数据持久化，保持纯净
- **Service层**：专注于业务逻辑，通过注解声明工作流需求
- **AOP切面**：专注于工作流处理，横切关注点

### 2. 声明式编程
通过注解的方式声明工作流需求，代码更加清晰和易于维护。

## 架构组件

### 1. 注解层 (@WorkflowEnabled)
```java
@WorkflowEnabled(
    historyType = HistoryType.POSITION, 
    entityType = ProjectPosition.class, 
    projectIdField = "projectId", 
    isCreate = true, 
    description = "新增项目岗位"
)
public void savePosition(ProjectPositionCreateDto dto) {
    // 业务逻辑
}
```

**注解参数说明：**
- `historyType`: 历史记录类型
- `entityType`: 实体类型
- `projectIdField`: 项目ID字段名
- `isCreate`: 是否为新增操作
- `description`: 操作描述

### 2. AOP切面 (WorkflowAspect)
```java
@Around("@annotation(workflowEnabled)")
public Object handleWorkflow(ProceedingJoinPoint joinPoint, WorkflowEnabled workflowEnabled)
```

**核心职责：**
- 拦截标记了@WorkflowEnabled的方法
- 判断是否需要工作流处理
- 工作流开启时：创建历史记录，跳过原方法执行
- 工作流关闭时：直接执行原方法

### 3. 工作流上下文 (WorkflowContext)
使用ThreadLocal管理工作流上下文信息，确保线程安全。

### 4. 工作流服务 (WorkflowService)
提供工作流判断逻辑，支持项目级别的工作流配置。

## 实现流程

### 工作流关闭时
```
Service方法调用 → AOP拦截 → 判断工作流状态 → 直接执行原方法 → 保存到数据库
```

### 工作流开启时
```
Service方法调用 → AOP拦截 → 判断工作流状态 → 创建历史记录 → 启动工作流 → 返回临时ID
```

## 关键特性

### 1. 完全解耦
- Repository层无任何工作流相关代码
- Service层只需添加注解，无需修改业务逻辑
- 工作流逻辑完全由AOP切面处理

### 2. 声明式配置
- 通过注解声明工作流需求
- 配置清晰，易于理解和维护

### 3. 灵活扩展
- 可以轻松扩展到其他实体类型
- 支持不同的工作流策略
- 可以集成各种工作流引擎

### 4. 透明处理
- 对调用方完全透明
- 返回值保持一致
- 异常处理机制完善

## 使用方式

### 1. 在Service方法上添加注解
```java
@WorkflowEnabled(
    historyType = HistoryType.POSITION,
    entityType = ProjectPosition.class,
    projectIdField = "projectId",
    isCreate = true,
    description = "新增项目岗位"
)
public void savePosition(ProjectPositionCreateDto dto) {
    // 原有业务逻辑保持不变
}
```

### 2. 配置项目工作流
```java
ProjectSetting setting = new ProjectSetting();
setting.setProjectId(projectId);
setting.setPositionApprovalFlow(true); // 开启工作流
```

### 3. 正常调用Service方法
```java
// 调用方式完全不变
projectPositionService.savePosition(createDto);
```

## 优势对比

### 相比Repository层耦合方案：
1. **更好的分离关注点**：Repository专注数据访问，AOP专注工作流
2. **更高的可维护性**：工作流逻辑集中管理，易于修改和扩展
3. **更强的可测试性**：可以独立测试Repository和工作流逻辑
4. **更好的可读性**：通过注解清晰表达工作流意图

### 相比传统Service层处理：
1. **减少代码重复**：工作流逻辑复用，避免在每个方法中重复编写
2. **降低出错概率**：统一的工作流处理逻辑，减少人为错误
3. **提高开发效率**：只需添加注解，无需编写工作流处理代码

## 扩展性

### 1. 支持更多实体类型
```java
@WorkflowEnabled(historyType = HistoryType.SUPPLIER, entityType = Supplier.class)
public void saveSupplier(SupplierDto dto) { ... }
```

### 2. 支持复杂工作流策略
```java
@WorkflowEnabled(
    historyType = HistoryType.POSITION,
    workflowStrategy = "MULTI_LEVEL_APPROVAL"
)
```

### 3. 集成外部工作流引擎
可以在AOP切面中集成Activiti、Flowable等工作流引擎。

## 测试验证

提供了完整的测试用例验证AOP工作流的正确性：
- 测试工作流关闭时的直接保存
- 测试工作流开启时的历史记录缓存
- 测试不同实体类型的工作流处理

## 总结

基于AOP的工作流实现方案实现了真正的关注点分离，通过声明式的方式管理工作流需求，提供了更加优雅、可维护和可扩展的解决方案。这种设计不仅满足了当前的需求，还为未来的扩展提供了良好的基础。
