# 岗位管理功能增强

## 概述
本次修改为岗位管理分页查询功能增加了项目设置信息和单独审批属性，满足了业务需求中"增加查询所属项目的设置，并为vo设置单独审批属性"的要求。

## 修改内容

### 1. PositionManagementPageVO 增强

**文件**: `src/main/java/com/caidaocloud/vms/application/vo/PositionManagementPageVO.java`

**新增字段**:
```java
@ApiModelProperty(value = "项目设置信息")
private ProjectSettingVO projectSetting;

@ApiModelProperty(value = "是否需要单独审批")
private Boolean requiresIndividualApproval;
```

**新增导入**:
```java
import com.caidaocloud.vms.application.vo.ProjectSettingVO;
```

### 2. PositionManagementService 增强

**文件**: `src/main/java/com/caidaocloud/vms/application/service/PositionManagementService.java`

**新增依赖注入**:
```java
@Autowired
private ProjectSettingRepository projectSettingRepository;
```

**新增导入**:
```java
import com.caidaocloud.vms.application.vo.ProjectSettingVO;
import com.caidaocloud.vms.domain.project.entity.ProjectSetting;
import com.caidaocloud.vms.domain.project.repository.ProjectSettingRepository;
```

**修改 assemblePositionPageVO 方法**:
1. 增加项目设置的批量查询:
```java
List<ProjectSetting> projectSettings = projectSettingRepository.loadList(projectIds);
```

2. 在VO组装过程中添加项目设置信息:
```java
// 组装项目设置信息
Option<ProjectSetting> projectSettingOpt = Sequences.sequence(projectSettings)
        .find(setting -> position.getProjectId().equals(setting.getProjectId()));
if (projectSettingOpt.isDefined()) {
    ProjectSetting projectSetting = projectSettingOpt.get();
    vo.setProjectSetting(ObjectConverter.convert(projectSetting, ProjectSettingVO.class));
    // 设置是否需要单独审批
    vo.setRequiresIndividualApproval(projectSetting.getPositionApprovalFlow());
}
```

## 功能特性

### 1. 项目设置查询
- 在岗位管理分页查询中，现在会同时返回每个岗位所属项目的完整设置信息
- 包括预算控制、报价功能、人数控制、自动关闭等所有项目设置属性
- 使用批量查询优化性能，避免N+1查询问题

### 2. 单独审批属性
- 新增 `requiresIndividualApproval` 字段，直接反映该岗位是否需要单独审批
- 该字段基于项目设置中的 `positionApprovalFlow` 属性设置
- 便于前端直接判断和显示审批流程状态

## 技术实现

### 1. 性能优化
- 使用批量查询 `projectSettingRepository.loadList(projectIds)` 获取所有相关项目设置
- 避免了在循环中逐个查询项目设置的性能问题
- 保持了现有的批量查询模式一致性

### 2. 数据一致性
- 通过项目ID关联项目设置，确保数据的准确性
- 使用 `Option` 模式安全处理可能不存在的项目设置
- 保持了现有代码的错误处理模式

### 3. 代码结构
- 遵循现有的代码结构和命名规范
- 使用相同的对象转换模式 `ObjectConverter.convert()`
- 保持了服务层的职责分离

## 使用示例

查询岗位管理分页数据时，返回的 `PositionManagementPageVO` 现在包含：

```json
{
  "bid": "position123",
  "projectId": "project456",
  "projectName": "系统升级项目",
  "projectCode": "PRJ-2025-001",
  "positionName": "Java开发工程师",
  "projectSetting": {
    "budgetEnabled": true,
    "quoteEnabled": true,
    "headcountEnabled": false,
    "positionAutoClose": true,
    "projectAutoClose": false,
    "positionApprovalFlow": true,
    "preHireEnabled": false
  },
  "requiresIndividualApproval": true
}
```

## 兼容性

- 新增字段为可选字段，不影响现有API的向后兼容性
- 现有的查询逻辑和返回结构保持不变
- 新增功能对现有业务逻辑无影响

## 测试建议

1. 验证项目设置信息正确返回
2. 验证单独审批属性与项目设置中的 `positionApprovalFlow` 一致
3. 验证批量查询性能
4. 验证在项目设置不存在时的处理逻辑
