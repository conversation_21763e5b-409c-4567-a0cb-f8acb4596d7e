# ProjectHistoryFactory增强实现

## 📋 概述

本文档描述了ProjectHistoryFactory的增强实现，新增了根据原始数据和ProjectDraft生成HistoryDetail的功能。这个增强使得工作流系统能够从草稿数据中生成完整的历史变更记录。

## 🎯 新增功能

### 1. 根据草稿生成历史详情记录
- **单个草稿处理**：`generateHistoryDetailFromDraft()`
- **批量草稿处理**：`generateHistoryDetailsFromDrafts()`
- **智能实体反序列化**：根据历史类型自动确定实体类型
- **变更记录生成**：对比原始数据和草稿数据生成变更记录

### 2. 完整的实体类型支持
- ✅ **ProjectPosition**（项目岗位）
- ✅ **Project**（项目基本信息）
- ✅ **ProjectContact**（项目联系人）
- ✅ **ProjectSupplier**（项目供应商）
- ✅ **ProjectSetting**（项目设置）

## 🏗️ 核心方法

### 1. generateHistoryDetailFromDraft()

```java
/**
 * 根据原始数据和ProjectDraft生成HistoryDetail
 * 
 * @param originalEntity 原始实体数据
 * @param projectDraft 项目草稿
 * @param historyId 历史记录ID
 * @return 历史详情记录
 */
public ProjectHistoryDetail generateHistoryDetailFromDraft(
    Object originalEntity, ProjectDraft projectDraft, String historyId)
```

**功能特点**：
- 从草稿中提取操作类型和历史类型
- 反序列化草稿中的实体数据
- 创建相应的历史详情记录（ProjectHistoryDetail或PositionHistoryDetail）
- 生成原始数据与草稿数据的变更记录
- 设置完整的快照和结果数据

### 2. generateHistoryDetailsFromDrafts()

```java
/**
 * 批量根据草稿生成历史详情记录
 * 
 * @param projectId 项目ID
 * @param historyType 历史类型
 * @param historyId 历史记录ID
 * @return 历史详情记录列表
 */
public List<ProjectHistoryDetail> generateHistoryDetailsFromDrafts(
    String projectId, HistoryType historyType, String historyId)
```

**功能特点**：
- 批量获取指定项目和类型的所有草稿
- 为每个草稿获取对应的原始实体数据
- 批量生成历史详情记录
- 支持大批量数据处理

## 🔧 辅助方法

### 1. deserializeDraftEntity()
```java
private Object deserializeDraftEntity(ProjectDraft projectDraft, HistoryType historyType)
```
- 根据历史类型确定实体类型
- 使用Jackson ObjectMapper反序列化草稿快照
- 支持所有项目相关实体类型

### 2. getEntityClassByHistoryType()
```java
private Class<?> getEntityClassByHistoryType(HistoryType historyType)
```
- 映射历史类型到具体的实体类
- 支持扩展新的实体类型
- 类型安全的实体类获取

### 3. createHistoryDetail()
```java
private ProjectHistoryDetail createHistoryDetail(String historyId, HistoryType historyType, OperationType operationType)
```
- 根据历史类型创建相应的详情记录
- 自动设置历史ID、类型和操作类型
- 支持ProjectHistoryDetail和PositionHistoryDetail

### 4. getOriginalEntity()
```java
private Object getOriginalEntity(String targetId, HistoryType historyType)
```
- 获取原始实体数据用于对比
- 处理临时ID（新增操作）的特殊情况
- 预留扩展接口，可注入具体的Repository实现

## 📊 处理流程

### 单个草稿处理流程
```
草稿数据 → 提取类型信息 → 反序列化实体 → 创建历史详情 → 生成变更记录 → 设置快照数据 → 返回历史详情
```

### 批量草稿处理流程
```
项目ID + 历史类型 → 查询草稿列表 → 循环处理每个草稿 → 获取原始数据 → 生成历史详情 → 汇总结果列表
```

## 🎨 设计优势

### 1. 类型安全
- 使用枚举类型确保类型安全
- 编译时检查，避免运行时错误
- 清晰的类型映射关系

### 2. 可扩展性
- 新增实体类型只需在`getEntityClassByHistoryType()`中添加映射
- 支持不同类型的历史详情记录
- 预留原始数据获取的扩展接口

### 3. 错误处理
- 完善的异常处理机制
- 详细的错误日志记录
- 优雅的错误降级处理

### 4. 性能优化
- 批量处理减少数据库访问
- 高效的JSON序列化/反序列化
- 合理的内存使用

## 🧪 测试用例

### 1. 单个草稿测试
```java
@Test
public void testGenerateHistoryDetailFromDraft() {
    // 测试根据草稿生成历史详情记录
    // 验证历史ID、类型、操作类型匹配
    // 验证快照和结果数据正确性
    // 验证变更记录生成
}
```

### 2. 批量草稿测试
```java
@Test
public void testGenerateHistoryDetailsFromDrafts() {
    // 测试批量生成历史详情记录
    // 验证批量处理的正确性
    // 验证每个记录的完整性
}
```

## 🚀 使用示例

### 1. 单个草稿处理
```java
@Autowired
private ProjectHistoryFactory projectHistoryFactory;

// 获取原始实体数据
ProjectPosition originalPosition = getOriginalPosition(targetId);

// 获取草稿数据
ProjectDraft draft = projectDraftRepository.getByTargetId(targetId).orElse(null);

// 生成历史详情记录
ProjectHistoryDetail detail = projectHistoryFactory.generateHistoryDetailFromDraft(
    originalPosition, draft, historyId);
```

### 2. 批量草稿处理
```java
// 批量生成指定项目的岗位历史详情记录
List<ProjectHistoryDetail> details = projectHistoryFactory.generateHistoryDetailsFromDrafts(
    projectId, HistoryType.POSITION, historyId);

// 保存历史详情记录
for (ProjectHistoryDetail detail : details) {
    historyDetailRepository.save(detail);
}
```

## 🔮 工作流集成

### 1. 工作流审批完成后
```java
// 工作流审批通过后，从草稿生成历史记录
public void onWorkflowApproved(String projectId, String historyId) {
    // 生成各类型的历史详情记录
    List<ProjectHistoryDetail> positionDetails = projectHistoryFactory
        .generateHistoryDetailsFromDrafts(projectId, HistoryType.POSITION, historyId);
    
    List<ProjectHistoryDetail> contactDetails = projectHistoryFactory
        .generateHistoryDetailsFromDrafts(projectId, HistoryType.CONTACT, historyId);
    
    // 保存历史记录并清理草稿
    saveHistoryDetails(positionDetails);
    saveHistoryDetails(contactDetails);
    cleanupDrafts(projectId);
}
```

### 2. 工作流拒绝后
```java
// 工作流拒绝后，清理草稿数据
public void onWorkflowRejected(String projectId) {
    projectDraftRepository.deleteByProjectId(projectId);
}
```

## 📝 注意事项

1. **实体类兼容性**：确保实体类支持Jackson序列化/反序列化
2. **原始数据获取**：需要根据实际需求实现`getOriginalEntity()`方法
3. **内存管理**：批量处理时注意内存使用，可考虑分页处理
4. **事务管理**：历史记录生成应在事务中进行
5. **错误恢复**：处理序列化失败等异常情况

## 🔧 配置要求

### 1. Spring配置
```java
@Component
public class ProjectHistoryFactory {
    @Autowired
    private ProjectDraftRepository projectDraftRepository;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
}
```

### 2. 依赖注入
- ProjectDraftRepository：草稿数据访问
- ObjectMapper：JSON序列化/反序列化
- 各种实体Repository（可选，用于获取原始数据）

---

这个ProjectHistoryFactory增强实现提供了完整的草稿到历史记录的转换能力，与工作流系统完美集成，支持多种实体类型的批量处理，为复杂的业务审批流程提供了强大的历史记录管理功能。
