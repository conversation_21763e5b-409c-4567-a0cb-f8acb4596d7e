package com.caidaocloud.vms.infrastructure.repository.base;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.PropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.dto.SimplePropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.vms.application.dto.base.EmpInfoDto;
import com.caidaocloud.vms.domain.base.repository.EmpRepository;
import com.caidaocloud.vms.infrastructure.util.DataUtil;
import com.googlecode.totallylazy.Sequences;
import org.apache.commons.collections.CollectionUtils;

import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR>
 * @date 2025/10/14
 */
@Repository
public class EmpRepositoryImpl implements EmpRepository {
	@Override
	public List<EmpInfoDto> loadEmpList(List<String> empIds) {
		empIds = Sequences.sequence(empIds).filter(Objects::nonNull).toList();
		if (CollectionUtils.isEmpty(empIds)) {
			return new ArrayList<>();
		}
		List<DataSimple> list = DataQuery.identifier("entity.hr.EmpWorkInfo").limit(-1, 1)
				.filter(DataFilter.in("empId", empIds).andNe("deleted", Boolean.TRUE.toString()), DataSimple.class)
				.getItems();

		return Sequences.sequence(list).map(data -> DataUtil.convert(data, EmpInfoDto.class)).toList();

	}

	@Override
	public Optional<EmpInfoDto> loadEmp(String empId) {
		List<DataSimple> list = DataQuery.identifier("entity.hr.EmpWorkInfo").limit(-1, 1)
				.filter(DataFilter.eq("empId", empId).andNe("deleted", Boolean.TRUE.toString()), DataSimple.class)
				.getItems();
		return list.isEmpty() ? Optional.empty() : Optional.of(DataUtil.convert(list.get(0), EmpInfoDto.class));
	}
}
