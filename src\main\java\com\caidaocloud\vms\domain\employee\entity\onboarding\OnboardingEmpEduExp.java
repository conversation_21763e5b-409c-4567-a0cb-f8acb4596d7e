package com.caidaocloud.vms.domain.employee.entity.onboarding;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.vms.domain.base.entity.BaseEntity;
import lombok.Data;

@Data
public class OnboardingEmpEduExp extends BaseEntity {

    private String empId;

    private String school;

    private String address;

    private Long startDate;

    private Long endDate;

    private Boolean degreeReceived = false;

    private EnumSimple lastYear; 

    private DictSimple degree;

    private DictSimple background;

    private String major;

    private String minor;

    private EnumSimple educationForm; 

    private String certificateNumber;

    private EnumSimple partTimeType; 

    private String remark;

    public static String identifier = "entity.vms.OnboardingEmpEduExp";

    @Override
    public String getEntityIdentifier() {
        return identifier;
    }
}