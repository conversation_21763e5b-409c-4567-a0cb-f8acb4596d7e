package com.caidaocloud.vms.application.vo;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel(description = "项目供应商信息")
public class ProjectSupplierVO {
    @ApiModelProperty(value = "关系ID")
    private String bid;

    @ApiModelProperty(value = "供应商ID")
    private String supplierId;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;


    @ApiModelProperty("供应商联系人")
    private String supplierContact;
    @ApiModelProperty("供应商联系人姓名")
    private String supplierContactName;

    @ApiModelProperty(value = "员工规模")
    private DictSimple staffSize;

    @ApiModelProperty(value = "收入规模")
    private DictSimple revenueScale;

    @ApiModelProperty(value = "擅长行业")
    private List<DictSimple> industry;

    @ApiModelProperty(value = "合同有效期")
    private Long endDate;

    @ApiModelProperty(value = "已合作年份")
    private BigDecimal year;

}