# HistoryFormat接口设计方案

## 📋 概述

本文档描述了基于接口和继承的HistoryFormat能力实现方案，通过面向对象的设计原则，消除了if判断，实现了更优雅的历史格式化功能。

## 🎯 设计目标

### 1. 消除if判断
- 不再使用if-else判断实体类型来选择格式化策略
- 通过多态机制实现自动的策略选择
- 提高代码的可维护性和扩展性

### 2. 面向对象设计
- 使用接口定义统一的格式化能力
- 通过抽象类实现两种不同的格式化策略
- 让具体实体类实现相应的格式化能力

### 3. 策略模式应用
- 详细格式化策略：适用于单一实体（Project、ProjectSetting）
- 简化格式化策略：适用于集合实体（ProjectPosition、ProjectSupplier、ProjectContact）

## 🏗️ 架构设计

### 1. 接口层次结构

```
HistoryFormat<T>                    // 顶层接口
├── DetailedHistoryFormat<T>        // 详细格式化抽象类
└── SimplifiedHistoryFormat<T>      // 简化格式化抽象类
```

### 2. 实体实现关系

```
Project implements HistoryFormat<Project>
├── 使用组合模式包含 DetailedHistoryFormat<Project>
└── 提供详细的字段变更记录

ProjectPosition implements HistoryFormat<ProjectPosition>
├── 使用组合模式包含 SimplifiedHistoryFormat<ProjectPosition>
└── 提供简化的操作描述

ProjectSupplier implements HistoryFormat<ProjectSupplier>
├── 使用组合模式包含 SimplifiedHistoryFormat<ProjectSupplier>
└── 提供简化的操作描述

ProjectContact implements HistoryFormat<ProjectContact>
├── 使用组合模式包含 SimplifiedHistoryFormat<ProjectContact>
└── 提供简化的操作描述
```

## 🔧 核心组件

### 1. HistoryFormat接口

```java
public interface HistoryFormat<T> {
    /**
     * 将ProjectDraft格式化为ProjectChange列表
     */
    List<ProjectChange> format(ProjectDraft draft);
}
```

**设计特点**：
- 泛型接口，支持类型安全
- 单一职责：只负责格式化功能
- 简洁明了：只有一个核心方法

### 2. DetailedHistoryFormat抽象类

```java
public abstract class DetailedHistoryFormat<T> implements HistoryFormat<T> {
    // 详细格式化实现
    protected abstract T loadOrigin(String targetId);
    protected abstract Class<T> getEntityClass();
    protected boolean shouldSkipField(String fieldName);
    protected String getFieldDisplayName(String fieldName);
}
```

**功能特点**：
- 实现详细的字段级变更记录
- 支持字段过滤和显示名称自定义
- 提供反射机制进行字段比较
- 适用于需要详细审计的实体

### 3. SimplifiedHistoryFormat抽象类

```java
public abstract class SimplifiedHistoryFormat<T> implements HistoryFormat<T> {
    // 简化格式化实现
    protected abstract Class<T> getEntityClass();
    protected abstract String getEntityDisplayName(T entity);
    protected abstract String getEntityTypeName();
}
```

**功能特点**：
- 实现简化的操作描述记录
- 只显示"操作类型+实体类型+实体名称"
- 不记录具体的字段变更
- 适用于集合类型的实体

## 💡 实现亮点

### 1. 组合模式的应用

由于Java单继承的限制，实体类需要继承各自的基类（如DataSimple、BaseEntity），因此采用组合模式来实现HistoryFormat能力：

```java
@Data
public class Project extends DataSimple implements HistoryFormat<Project> {
    
    // 使用组合模式实现HistoryFormat接口
    private final DetailedHistoryFormat<Project> historyFormatter = new DetailedHistoryFormat<Project>() {
        @Override
        protected Project loadOrigin(String targetId) {
            // 实现原始数据加载逻辑
            return null;
        }

        @Override
        protected Class<Project> getEntityClass() {
            return Project.class;
        }

        @Override
        protected String getFieldDisplayName(String fieldName) {
            // 提供中文字段名称映射
            switch (fieldName) {
                case "projectName": return "项目名称";
                case "budget": return "预算";
                default: return fieldName;
            }
        }
    };

    @Override
    public List<ProjectChange> format(ProjectDraft draft) {
        return historyFormatter.format(draft);
    }
}
```

### 2. 多态机制的运用

在ProjectHistoryFactory中，不再需要if判断，直接使用多态：

```java
private List<ProjectChange> generateChangesUsingEntityFormat(Object draftEntity, ProjectDraft projectDraft) {
    // 检查实体是否实现了HistoryFormat接口
    if (draftEntity instanceof HistoryFormat) {
        @SuppressWarnings("unchecked")
        HistoryFormat<Object> historyFormat = (HistoryFormat<Object>) draftEntity;
        return historyFormat.format(projectDraft);
    } else {
        // 使用默认格式化
        return generateDefaultChanges(projectDraft);
    }
}
```

### 3. 智能实体描述生成

简化格式化抽象类提供了智能的实体描述生成：

```java
// ProjectPosition的实现
@Override
protected String getEntityDisplayName(ProjectPosition entity) {
    if (entity != null && entity.getPosition() != null) {
        return entity.getPosition();  // 返回"Java开发工程师"
    }
    return null;
}

@Override
protected String getEntityTypeName() {
    return "岗位";
}

// 最终生成："新增岗位Java开发工程师"
```

## 📊 格式化效果对比

### 详细格式化（Project）

```
变更记录：
- projectName: null -> "测试项目"
- budget: null -> 100000
- plannedHeadcount: null -> 10
- startDate: null -> 1697875200000
- company: null -> "测试公司"
```

### 简化格式化（ProjectPosition）

```
变更记录：
- 新增岗位Java开发工程师
```

### 简化格式化（ProjectSupplier）

```
变更记录：
- 新增供应商ABC公司
```

### 简化格式化（ProjectContact）

```
变更记录：
- 新增联系人张三
```

## 🧪 测试验证

### 1. 单元测试覆盖

- **详细格式化测试**：验证Project的详细字段变更记录
- **简化格式化测试**：验证Position、Supplier、Contact的简化记录
- **多态性测试**：验证接口的多态调用能力
- **组合模式测试**：验证组合模式的正确实现

### 2. 测试用例示例

```java
@Test
public void testProjectDetailedHistoryFormat() {
    Project project = new Project("PRJ001", "测试项目");
    ProjectDraft draft = createProjectDraft(project, HistoryType.BASIC_INFO, OperationType.CREATE);
    
    List<ProjectChange> changes = project.format(draft);
    
    // 验证是详细格式化
    assertTrue(changes.size() > 1);
    assertTrue(changes.stream().anyMatch(change -> 
        change.getFieldName().equals("projectName")));
}

@Test
public void testProjectPositionSimplifiedHistoryFormat() {
    ProjectPosition position = new ProjectPosition("project123", "Java开发工程师", "测试公司", "技术部");
    ProjectDraft draft = createProjectDraft(position, HistoryType.POSITION, OperationType.CREATE);
    
    List<ProjectChange> changes = position.format(draft);
    
    // 验证是简化格式化
    assertEquals(1, changes.size());
    assertTrue(changes.get(0).getFieldName().contains("新增岗位Java开发工程师"));
}
```

## 🚀 使用方式

### 1. 实体格式化调用

```java
// 多态调用，无需if判断
HistoryFormat<?> entity = getEntityFromDraft(draft);
List<ProjectChange> changes = entity.format(draft);
```

### 2. 工厂方法集成

```java
// 在ProjectHistoryFactory中
public ProjectHistoryDetail generateHistoryDetailFromDraft(Object originalEntity, ProjectDraft projectDraft, String historyId) {
    // 反序列化草稿中的实体数据
    Object draftEntity = deserializeDraftEntity(projectDraft, historyType);
    
    // 使用实体的format方法生成变更记录（无需if判断）
    List<ProjectChange> changes = generateChangesUsingEntityFormat(draftEntity, projectDraft);
    
    // 设置到历史详情记录中
    detail.setChange(changes);
    return detail;
}
```

## 🎨 设计优势

### 1. 面向对象原则

- **单一职责**：每个类只负责自己的格式化逻辑
- **开闭原则**：对扩展开放，对修改封闭
- **里氏替换**：子类可以替换父类使用
- **接口隔离**：接口职责单一明确
- **依赖倒置**：依赖抽象而不是具体实现

### 2. 代码质量提升

- **消除if判断**：使用多态机制替代条件判断
- **提高可维护性**：新增实体类型只需实现接口
- **增强可扩展性**：易于添加新的格式化策略
- **类型安全**：泛型接口提供编译时类型检查

### 3. 性能优化

- **减少分支判断**：多态调用比if-else更高效
- **内存友好**：组合模式避免了深层继承
- **缓存友好**：相同类型的格式化逻辑集中在一起

## 🔮 扩展方向

### 1. 新增实体类型

```java
// 新增实体只需实现HistoryFormat接口
public class ProjectTask extends BaseEntity implements HistoryFormat<ProjectTask> {
    
    private final SimplifiedHistoryFormat<ProjectTask> historyFormatter = new SimplifiedHistoryFormat<ProjectTask>() {
        @Override
        protected Class<ProjectTask> getEntityClass() {
            return ProjectTask.class;
        }

        @Override
        protected String getEntityDisplayName(ProjectTask entity) {
            return entity.getTaskName();
        }

        @Override
        protected String getEntityTypeName() {
            return "任务";
        }
    };

    @Override
    public List<ProjectChange> format(ProjectDraft draft) {
        return historyFormatter.format(draft);
    }
}
```

### 2. 新增格式化策略

```java
// 可以添加新的格式化策略
public abstract class SummaryHistoryFormat<T> implements HistoryFormat<T> {
    // 摘要格式化实现
    // 显示关键字段的变更摘要
}
```

### 3. 智能化增强

- **AI描述生成**：使用AI生成更自然的变更描述
- **多语言支持**：支持多语言的格式化输出
- **个性化配置**：根据用户偏好调整格式化策略

---

这个基于接口和继承的HistoryFormat设计方案完全消除了if判断，通过面向对象的设计原则实现了更优雅、更可维护的历史格式化功能。每个实体类都拥有自己的格式化能力，通过多态机制实现统一的调用接口，真正体现了"让对象自己知道如何格式化自己"的设计理念。
