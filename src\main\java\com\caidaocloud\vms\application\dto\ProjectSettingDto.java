package com.caidaocloud.vms.application.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 项目设置DTO
 * 
 * <AUTHOR>
 * @date 2025/9/27
 */
@Data
@ApiModel(description = "项目设置信息")
public class ProjectSettingDto {
    
    @ApiModelProperty(value = "设置ID")
    private String bid;
    
    @ApiModelProperty(value = "项目ID", required = true)
    private String projectId;
    
    @ApiModelProperty(value = "是否启用预算控制", example = "false")
    private Boolean budgetEnabled = false;
    
    @ApiModelProperty(value = "是否启用报价功能", example = "false")
    private Boolean quoteEnabled = false;
    
    @ApiModelProperty(value = "是否启用人数控制", example = "false")
    private Boolean headcountEnabled = false;
    
    @ApiModelProperty(value = "岗位是否自动关闭", example = "false")
    private Boolean positionAutoClose = false;
    
    @ApiModelProperty(value = "项目是否自动关闭", example = "false")
    private Boolean projectAutoClose = false;
    
    @ApiModelProperty(value = "岗位是否需要审批流程", example = "false")
    private Boolean positionApprovalFlow = false;
    
    @ApiModelProperty(value = "是否启用预聘功能", example = "false")
    private Boolean preHireEnabled = false;
}
