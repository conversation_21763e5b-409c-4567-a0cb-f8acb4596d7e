package com.caidaocloud.vms.application.dto;

import com.caidaocloud.dto.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "项目查询条件")
public class ProjectQueryDTO extends BasePage {
    @ApiModelProperty(value = "项目名称", example = "系统升级")
    private String projectName;
    
    @ApiModelProperty(value = "项目状态", example = "1")
    private Integer status;
}