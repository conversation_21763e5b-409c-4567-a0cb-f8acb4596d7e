package com.caidaocloud.vms;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ComponentScan;

/**
 *
 * <AUTHOR>
 * @date 2025/5/16
 */
@SpringBootApplication
@EnableDiscoveryClient
@ComponentScan(basePackages = { "com.caidaocloud.config","com.caidaocloud.vms"})
public class VMSApplication {
	public static void main(String[] args) {
		SpringApplication.run(VMSApplication.class, args);
	}
}
