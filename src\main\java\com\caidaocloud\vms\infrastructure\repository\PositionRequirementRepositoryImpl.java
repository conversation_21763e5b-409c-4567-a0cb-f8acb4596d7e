package com.caidaocloud.vms.infrastructure.repository;

import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.vms.domain.project.annotation.HistoryDetailRecord;
import com.caidaocloud.vms.domain.project.entity.PositionRequirement;
import com.caidaocloud.vms.domain.project.entity.PositionSupplier;
import com.caidaocloud.vms.domain.project.enums.HistoryType;
import com.caidaocloud.vms.domain.project.enums.OperationType;
import com.caidaocloud.vms.domain.project.repository.PositionRequirementRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public class PositionRequirementRepositoryImpl implements PositionRequirementRepository {

    @Override
    @HistoryDetailRecord(entityTypes = { PositionRequirement.class }, historyType = HistoryType.POSITION,subType = HistoryType.REQUIRE,operationType = OperationType.CREATE)
    public void saveOrUpdate(PositionRequirement positionRequirement) {
        if (positionRequirement.getBid() == null) {
            DataInsert.identifier(PositionRequirement.identifier).insert(positionRequirement);
        } else {
            DataUpdate.identifier(PositionRequirement.identifier).update(positionRequirement);
        }
    }

    @Override
    public Optional<PositionRequirement> getByPositionId(String positionId) {
        List<PositionRequirement> requirements = DataQuery.identifier(PositionRequirement.identifier)
                .limit(1, 1)
                .filter(DataFilter.ne("deleted", Boolean.TRUE.toString())
                        .andEq("positionId", positionId), PositionRequirement.class).getItems();
        
        return requirements.isEmpty() ? Optional.empty() : Optional.of(requirements.get(0));
    }

    @Override
    public void deleteByPositionId(String positionId) {
        List<PositionRequirement> requirements = DataQuery.identifier(PositionRequirement.identifier)
                .filter(DataFilter.ne("deleted", Boolean.TRUE.toString())
                        .andEq("positionId", positionId), PositionRequirement.class).getItems();
        
        for (PositionRequirement requirement : requirements) {
            DataDelete.identifier(PositionRequirement.identifier).delete(requirement.getBid());
        }
    }

    @Override
    public void init(PositionRequirement positionRequirement, String positionId) {
        positionRequirement.setBid(positionId);
        positionRequirement.setPositionId(positionId);
        DataInsert.identifier(PositionRequirement.identifier).insert(positionRequirement);
    }
}
