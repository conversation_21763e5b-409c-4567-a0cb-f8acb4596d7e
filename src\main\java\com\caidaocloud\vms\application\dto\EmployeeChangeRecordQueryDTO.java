package com.caidaocloud.vms.application.dto;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.vms.domain.base.enums.ApprovalStatus;
import com.caidaocloud.vms.domain.employee.enums.EmployeeChangeType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 员工入离项查询条件DTO
 * 
 * <AUTHOR>
 * @date 2025/10/29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "员工入离项查询条件")
public class EmployeeChangeRecordQueryDTO extends BasePage {

    @ApiModelProperty(value = "项目ID")
    private String projectId;

    @ApiModelProperty(value = "公司ID")
    private String companyId;

    @ApiModelProperty(value = "供应商ID")
    private String supplierId;

    @ApiModelProperty(value = "岗位名称（模糊查询）")
    private String positionName;

    @ApiModelProperty(value = "员工姓名（模糊查询）")
    private String empName;

    @ApiModelProperty(value = "交接类型：0-预入职，1-入职，2-续约，3-调薪，4-离职")
    private EmployeeChangeType changeType;

    @ApiModelProperty(value = "审批状态：0-未提交，1-审批中，2-已通过，3-修改待提交，4-已关闭，6-已拒绝")
    private ApprovalStatus approvalStatus;
}
