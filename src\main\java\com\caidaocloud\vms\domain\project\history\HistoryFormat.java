package com.caidaocloud.vms.domain.project.history;

import java.util.List;

import com.caidaocloud.vms.domain.project.entity.ProjectChange;
import com.caidaocloud.vms.domain.project.entity.ProjectDraft;
import com.caidaocloud.vms.domain.project.enums.OperationType;

/**
 * 历史格式化接口
 * 定义了将ProjectDraft转换为ProjectChange列表的能力
 *
 * <AUTHOR>
 * @date 2025/10/11
 */
public interface HistoryFormat<T> {

	/**
	 * 将ProjectDraft格式化为ProjectChange列表
	 *
	 * @param draft 项目草稿
	 * @return 变更记录列表
	 */
	List<ProjectChange> format(T originData,ProjectDraft draft);


	List<ProjectChange> format(ProjectDraft draft);

	String formatSummary(List<ProjectChange> changes, OperationType operationType);
}
