package com.caidaocloud.vms.infrastructure.repository;


import java.util.List;
import java.util.Optional;

import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.vms.domain.supplier.entity.SupplierContract;
import com.caidaocloud.vms.domain.supplier.repository.SupplierContractRepository;

import org.springframework.stereotype.Repository;

@Repository
public class SupplierContractRepositoryImpl implements SupplierContractRepository {

	@Override
	public String saveOrUpdate(SupplierContract supplierContract) {
		if (supplierContract.getBid() == null) {
			DataInsert.identifier(SupplierContract.identifier).insert(supplierContract);
		}
		else {
			DataUpdate.identifier(SupplierContract.identifier).update(supplierContract);
		}
		return supplierContract.getBid();
	}

	@Override
	public void deleteContract(String contractId) {
		DataDelete.identifier(SupplierContract.identifier).delete(contractId);
	}

	@Override
	public Optional<SupplierContract> getContract(String bid) {
		return Optional.ofNullable(DataQuery.identifier(SupplierContract.identifier)
				.oneOrNull(bid, SupplierContract.class));
	}

	@Override
	public List<SupplierContract> listContract(String supplierId) {

		return DataQuery.identifier(SupplierContract.identifier).limit(-1, 1)
				.filter(DataFilter.ne("deleted", Boolean.TRUE.toString())
						.andEq("supplierId", supplierId), SupplierContract.class).getItems();
	}
}