package com.caidaocloud.vms.domain.project.dto;

import java.util.ArrayList;
import java.util.List;

import com.caidaocloud.vms.domain.project.entity.ProjectChange;
import com.googlecode.totallylazy.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * formatAll方法结果DTO
 *
 * <AUTHOR>
 * @date 2025/10/17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FormatAllResult {
    
    /**
     * 格式化后的JSON快照
     */
    private String snapshot;
    
    /**
     * 变更记录列表
     */
    private List<ProjectChange> changes;

    private List<String> draftIdList=new ArrayList<>();

    private FormatAllResult(String snapshot, List<ProjectChange> changes) {
        this.snapshot = snapshot;
        this.changes = changes;
    }

    /**
     * 创建formatAll结果
     *
     * @param snapshot 格式化后的JSON快照
     * @param changes 变更记录列表
     * @return formatAll结果
     */
    public static FormatAllResult of(String snapshot, List<ProjectChange> changes, List<String> draftIdList) {
        return new FormatAllResult(snapshot, changes, draftIdList);
    }
    
    /**
     * 创建空的formatAll结果
     * 
     * @param snapshot JSON快照
     * @return formatAll结果
     */
    public static FormatAllResult empty(String snapshot) {
        return new FormatAllResult(snapshot, Lists.list());
    }
}
