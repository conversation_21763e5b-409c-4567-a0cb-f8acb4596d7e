package com.caidaocloud.vms;

import java.util.Arrays;
import java.util.List;

import com.caidaocloud.vms.application.dto.base.CompanyInfoDto;
import com.caidaocloud.vms.application.dto.base.OrgInfoDto;
import com.caidaocloud.vms.application.service.emp.CompanyService;
import com.caidaocloud.vms.application.service.emp.OrganizeService;
import org.junit.Test;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 公司和组织服务测试
 * 
 * <AUTHOR>
 * @date 2025/10/15
 */
@SpringBootTest
public class CompanyAndOrganizeServiceTest {

    @Autowired
    private CompanyService companyService;

    @Autowired
    private OrganizeService organizeService;

    @Test
    public void testLoadCompanyList() {
        // 测试加载公司列表
        List<String> companyIds = Arrays.asList("company1", "company2");
        List<CompanyInfoDto> companies = companyService.loadCompanyList(companyIds);
        
        System.out.println("Company list size: " + companies.size());
        for (CompanyInfoDto company : companies) {
            System.out.println("Company: " + company.getCompanyName() + " (" + company.getCompanyCode() + ")");
        }
    }

    @Test
    public void testLoadOrgList() {
        // 测试加载组织列表
        List<String> orgIds = Arrays.asList("org1", "org2");
        List<OrgInfoDto> orgs = organizeService.loadOrgList(orgIds);
        
        System.out.println("Organization list size: " + orgs.size());
        for (OrgInfoDto org : orgs) {
            System.out.println("Organization: " + org.getOrgName() + " (" + org.getOrgCode() + ")");
        }
    }
}
