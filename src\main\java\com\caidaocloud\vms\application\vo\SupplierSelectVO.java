package com.caidaocloud.vms.application.vo;

import java.math.BigDecimal;
import java.util.List;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.vms.domain.supplier.enums.SupplierStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SupplierSelectVO {

    @ApiModelProperty(value = "业务ID")
    private String bid;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

}