package com.caidaocloud.vms.infrastructure.repository;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.vms.domain.project.entity.ProjectHistory;
import com.caidaocloud.vms.domain.project.entity.ProjectHistoryDetail;
import com.caidaocloud.vms.domain.project.repository.ProjectHistoryRepository;

import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 项目历史Repository实现类
 * 
 * <AUTHOR>
 * @date 2025/9/25
 */
@Repository
public class ProjectHistoryRepositoryImpl implements ProjectHistoryRepository {

    @Override
    public String saveOrUpdate(ProjectHistory projectHistory) {
        if (projectHistory.getBid() == null) {
            DataInsert.identifier(ProjectHistory.identifier).insert(projectHistory);

            // 保存项目历史详情
            if (projectHistory.getDetailList() != null) {
                for (ProjectHistoryDetail detail : projectHistory.getDetailList()) {
                    detail.setHistoryId(projectHistory.getBid());
                    DataInsert.identifier(ProjectHistoryDetail.identifier).insert(detail);
                }
            }
        } else {
            DataUpdate.identifier(ProjectHistory.identifier).update(projectHistory);
        }
        return projectHistory.getBid();
    }

    @Override
    public Optional<ProjectHistory> getById(String historyId) {
        ProjectHistory history = DataQuery.identifier(ProjectHistory.identifier)
                .oneOrNull(historyId, ProjectHistory.class);

        // if (history != null) {
        // // 加载项目历史详情
        // List<ProjectHistoryDetail> detailList =
        // DataQuery.identifier(ProjectHistoryDetail.identifier)
        // .limit(-1, 1)
        // .filter(DataFilter.ne("deleted", Boolean.TRUE.toString())
        // .andEq("historyId", historyId), ProjectHistoryDetail.class).getItems();
        // history.setDetailList(detailList);
        //
        // // 加载岗位历史详情
        // List<PositionHistoryDetail> positionDetailList =
        // DataQuery.identifier(PositionHistoryDetail.identifier)
        // .limit(-1, 1)
        // .filter(DataFilter.ne("deleted", Boolean.TRUE.toString())
        // .andEq("historyId", historyId), PositionHistoryDetail.class).getItems();
        // history.setPositionDetailList(positionDetailList);
        // }

        return Optional.ofNullable(history);
    }

    @Override
    public List<ProjectHistory> getByProjectId(String projectId) {
        return DataQuery.identifier(ProjectHistory.identifier)
                .limit(-1, 1)
                .filter(DataFilter.ne("deleted", Boolean.TRUE.toString())
                        .andEq("projectId", projectId), ProjectHistory.class)
                .getItems();
    }

    @Override
    public PageResult<ProjectHistory> findByPage(String projectId, int pageSize, int pageNo) {
        return DataQuery.identifier(ProjectHistory.identifier)
                .limit(pageSize, pageNo)
                .filter(DataFilter.ne("deleted", Boolean.TRUE.toString())
                        .andEq("projectId", projectId), ProjectHistory.class);
    }

    @Override
    public void delete(String historyId) {
        DataDelete.identifier(ProjectHistory.identifier).delete(historyId);
    }

    @Override
    public void rollbackHistory(ProjectHistory projectHistory) {
        DataDelete.identifier(ProjectHistory.identifier).delete(projectHistory.getBid());
        DataDelete.identifier(ProjectHistoryDetail.identifier)
                .batchDelete(DataFilter.eq("historyId", projectHistory.getBid()));
    }

}
