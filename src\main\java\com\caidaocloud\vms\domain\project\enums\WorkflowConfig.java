package com.caidaocloud.vms.domain.project.enums;

import com.caidaocloud.workflow.enums.WfCallbackTimeTypeEnum;
import com.caidaocloud.workflow.enums.WfCallbackTypeEnum;
import com.caidaocloud.workflow.enums.WfFunctionPageJumpType;

/**
 * 工作流配置枚举
 * 定义工作流function和callback的配置信息
 *
 * <AUTHOR>
 * @date 2025/10/17
 */
public enum WorkflowConfig {

    /**
     * 项目管理工作流
     */
    PROJECT_MANAGEMENT(
            "项目管理",
            "VMS_PROJECT",
            "/api/vms/v1/project/detail",
            new CallbackConfig[] {
                    new CallbackConfig("APPROVE", "通过", "/api/vms/v1/project/workflow/approve"),
                    new CallbackConfig("REJECT", "拒绝", "/api/vms/v1/project/workflow/reject")
            }),

    /**
     * 项目岗位管理工作流
     */
    PROJECT_POSITION(
            "项目管理-岗位",
            "VMS_POSITION",
            "/api/vms/v1/project/detail",
            new CallbackConfig[] {
                    new CallbackConfig("APPROVE", "通过", "/api/vms/v1/project/position/workflow/approve"),
                    new CallbackConfig("REJECT", "拒绝", "/api/vms/v1/project/position/workflow/reject")
            }),

    /**
     * 员工预入职工作流
     */
    EMPLOYEE_ONBOARDING(
            "员工预入职",
            "VMS_ONBOARDING",
            "/api/vms/v1/employee/changeRecord/detail",
            new CallbackConfig[] {
                    new CallbackConfig("APPROVE", "通过", "/api/vms/v1/employee/onboarding/workflow/approve"),
                    new CallbackConfig("REJECT", "拒绝", "/api/vms/v1/employee/onboarding/workflow/reject")
            });

    private final String name;
    private final String code;
    private final String detailPath;
    private final CallbackConfig[] callbacks;

    WorkflowConfig(String name, String code, String detailPath, CallbackConfig[] callbacks) {
        this.name = name;
        this.code = code;
        this.detailPath = detailPath;
        this.callbacks = callbacks;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }

    public String getDetailPath() {
        return detailPath;
    }

    public CallbackConfig[] getCallbacks() {
        return callbacks;
    }

    /**
     * 回调配置内部类
     */
    public static class CallbackConfig {
        private final String action;
        private final String description;
        private final String path;

        public CallbackConfig(String action, String description, String path) {
            this.action = action;
            this.description = description;
            this.path = path;
        }

        public String getAction() {
            return action;
        }

        public String getDescription() {
            return description;
        }

        public String getPath() {
            return path;
        }

        /**
         * 获取回调代码
         */
        public String getCallbackCode() {
            return String.format("WF_CALLBACK_%s", action.toUpperCase());
        }

        /**
         * 获取回调名称
         */
        public String getCallbackName(String workflowName) {
            return String.format("%s-%s", workflowName, description);
        }

        /**
         * 获取回调地址
         */
        public String getCallbackAddress() {
            return path;
        }
    }

    /**
     * 根据代码查找工作流配置
     */
    public static WorkflowConfig fromCode(String code) {
        for (WorkflowConfig config : values()) {
            if (config.code.equals(code)) {
                return config;
            }
        }
        throw new IllegalArgumentException("Unknown workflow code: " + code);
    }
}
