package com.caidaocloud.vms.domain.project.repository;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.vms.application.dto.PositionPriceLibraryQueryDto;
import com.caidaocloud.vms.domain.project.entity.PositionPriceLibrary;

import java.util.Optional;

/**
 * 岗位价格库Repository接口
 *
 * <AUTHOR>
 * @date 2025/12/10
 */
public interface PositionPriceLibraryRepository {

    /**
     * 保存或更新岗位价格库
     *
     * @param library 岗位价格库实体
     * @return 主键ID
     */
    String saveOrUpdate(PositionPriceLibrary library);

    /**
     * 根据ID获取岗位价格库
     *
     * @param bid 主键ID
     * @return 岗位价格库实体
     */
    Optional<PositionPriceLibrary> getById(String bid);

    /**
     * 分页查询岗位价格库
     *
     * @param queryDto 查询条件
     * @return 分页结果
     */
    PageResult<PositionPriceLibrary> findByPage(PositionPriceLibraryQueryDto queryDto);

    /**
     * 检查岗位和组织的价格库是否已存在
     *
     * @param postId     岗位ID
     * @param orgId      组织ID
     * @param excludeBid 排除的ID（用于更新时检查）
     * @return 是否存在
     */
    boolean existsByPostIdAndOrgId(String postId, String orgId, String excludeBid);

    /**
     * 根据岗位ID和组织ID查找价格库
     *
     * @param postId 岗位ID
     * @param orgId  组织ID
     * @return 岗位价格库实体
     */
    Optional<PositionPriceLibrary> findByPostIdAndOrgId(String postId, String orgId);

	void delete(PositionPriceLibrary library);
}
