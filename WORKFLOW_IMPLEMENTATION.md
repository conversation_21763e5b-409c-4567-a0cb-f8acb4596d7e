# 工作流集成实现文档

## 需求描述

若项目开启了工作流配置，再新增、编辑项目信息后，先将修改信息缓存到ProjectHistory中。
若没有开启工作流，则直接保存对应实体中。
要求在底层抽象中实现这个要求，不改动上层service代码。

## 实现方案

### 1. 核心设计思路

通过在Repository层集成工作流逻辑，实现对上层Service透明的工作流处理。当保存项目岗位时，自动判断是否开启工作流：
- 开启工作流：将数据缓存到ProjectHistory表，等待审批
- 未开启工作流：直接保存到实体表

### 2. 新增的组件

#### 2.1 Repository层
- `ProjectSettingRepository` - 项目设置数据访问接口
- `ProjectSettingRepositoryImpl` - 项目设置数据访问实现
- `ProjectHistoryRepository` - 项目历史数据访问接口  
- `ProjectHistoryRepositoryImpl` - 项目历史数据访问实现

#### 2.2 服务层
- `WorkflowService` - 工作流服务，负责判断是否需要走工作流
- 完善了`ProjectHistoryService` - 项目历史服务，处理工作流启动

#### 2.3 工厂类
- 完善了`ProjectHistoryFactory` - 项目历史工厂，支持创建岗位相关的历史记录

### 3. 核心实现逻辑

#### 3.1 工作流判断逻辑（WorkflowService）

```java
public boolean isPositionApprovalFlowEnabled(String projectId) {
    Optional<ProjectSetting> settingOpt = projectSettingRepository.getByProjectId(projectId);
    if (settingOpt.isPresent()) {
        ProjectSetting setting = settingOpt.get();
        return Boolean.TRUE.equals(setting.getPositionApprovalFlow());
    }
    return false;
}
```

#### 3.2 Repository层集成（ProjectPositionRepositoryImpl）

```java
@Override
public String saveOrUpdate(ProjectPosition projectPosition) {
    boolean isNewPosition = projectPosition.getBid() == null;
    
    // 为新增岗位生成临时ID
    if (isNewPosition && projectPosition.getBid() == null) {
        projectPosition.setBid("temp_" + SnowUtil.nextId());
    }
    
    // 判断是否需要走工作流
    boolean shouldSaveDirectly = isNewPosition ? 
        workflowService.handlePositionCreate(projectPosition) : 
        workflowService.handlePositionEdit(projectPosition);
    
    if (shouldSaveDirectly) {
        // 直接保存到实体表
        // ... 保存逻辑
    }
    // 如果不需要直接保存，说明已经缓存到历史记录中，等待审批
    
    return projectPosition.getBid();
}
```

### 4. 数据流程

#### 4.1 工作流关闭时
```
上层Service调用 → Repository.saveOrUpdate() → WorkflowService判断 → 直接保存到实体表
```

#### 4.2 工作流开启时  
```
上层Service调用 → Repository.saveOrUpdate() → WorkflowService判断 → 缓存到ProjectHistory → 启动工作流
```

### 5. 关键特性

#### 5.1 对上层透明
- 上层Service代码无需修改
- 工作流逻辑完全在底层处理
- 返回值保持一致

#### 5.2 ID管理
- 新增岗位时生成临时ID，确保上层能获得返回值
- 工作流关闭时，清除临时ID让数据库生成真实ID
- 工作流开启时，保留临时ID用于历史记录关联

#### 5.3 历史记录管理
- 使用JSON格式存储实体快照
- 支持项目和岗位两种类型的历史记录
- 自动设置审批状态和时间戳

### 6. 配置说明

项目设置中的关键字段：
- `positionApprovalFlow`: Boolean类型，控制岗位是否需要审批工作流

### 7. 测试验证

提供了完整的集成测试用例：
- 测试工作流关闭时的直接保存
- 测试工作流开启时的历史记录缓存
- 测试岗位编辑的工作流逻辑
- 测试项目设置查询功能

### 8. 扩展性

#### 8.1 支持更多实体类型
可以轻松扩展到其他实体（如项目基本信息、供应商等）的工作流处理

#### 8.2 工作流引擎集成
预留了工作流引擎集成接口，可以对接Activiti、Flowable等工作流引擎

#### 8.3 审批流程
提供了审批通过和拒绝的处理方法框架

### 9. 注意事项

1. **事务管理**: 确保工作流操作在同一事务中
2. **异常处理**: 工作流异常不应影响正常业务流程
3. **性能考虑**: 工作流判断逻辑应该高效，避免频繁数据库查询
4. **数据一致性**: 历史记录和实体数据的一致性保证

## 总结

本实现方案成功在底层抽象中集成了工作流逻辑，实现了对上层Service的透明处理。通过Repository层的智能路由，根据项目配置自动选择直接保存或工作流缓存，满足了需求中"不改动上层service代码"的要求。
