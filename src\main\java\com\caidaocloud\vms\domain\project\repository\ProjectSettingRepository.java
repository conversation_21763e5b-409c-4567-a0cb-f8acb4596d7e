package com.caidaocloud.vms.domain.project.repository;

import com.caidaocloud.vms.domain.project.entity.ProjectSetting;

import java.util.List;
import java.util.Optional;

/**
 * 项目设置Repository接口
 * 
 * <AUTHOR>
 * @date 2025/9/25
 */
public interface ProjectSettingRepository {

    /**
     * 保存或更新项目设置
     * @param projectSetting 项目设置
     * @return 设置ID
     */
    String saveOrUpdate(ProjectSetting projectSetting);

    /**
     * 根据项目ID获取项目设置
     * @param projectId 项目ID
     * @return 项目设置
     */
    Optional<ProjectSetting> getByProjectId(String projectId);

    /**
     * 根据ID获取项目设置
     * @param settingId 设置ID
     * @return 项目设置
     */
    Optional<ProjectSetting> getById(String settingId);

    /**
     * 删除项目设置
     * @param settingId 设置ID
     */
    void delete(String settingId);

    void init(ProjectSetting projectSetting, String projectBid);

	List<ProjectSetting> loadList(List<String> projectIds);
}
