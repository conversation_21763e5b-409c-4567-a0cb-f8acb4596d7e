package com.caidaocloud.vms.infrastructure.repository.base;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.vms.application.dto.base.OrgInfoDto;
import com.caidaocloud.vms.domain.base.repository.OrganizeRepository;
import com.caidaocloud.vms.infrastructure.util.DataUtil;
import com.googlecode.totallylazy.Sequences;
import org.apache.commons.collections.CollectionUtils;

import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR>
 * @date 2025/10/15
 */
@Repository
public class OrganizeRepositoryImpl implements OrganizeRepository {

	@Override
	public List<OrgInfoDto> loadOrgList(List<String> orgIds) {
		orgIds = Sequences.sequence(orgIds).filter(Objects::nonNull).toList();
		if (CollectionUtils.isEmpty(orgIds)) {
			return new ArrayList<>();
		}
		List<DataSimple> list = DataQuery.identifier("entity.hr.Org").limit(-1, 1)
				.filter(DataFilter.in("bid", orgIds).andNe("deleted", Boolean.TRUE.toString()), DataSimple.class)
				.getItems();

		return Sequences.sequence(list).map(data -> DataUtil.convert(data, OrgInfoDto.class)).toList();
	}
}
