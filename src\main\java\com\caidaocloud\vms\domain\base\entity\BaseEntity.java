package com.caidaocloud.vms.domain.base.entity;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.security.util.SecurityUserUtil;

/**
 *
 * <AUTHOR>
 * @date 2025/9/23
 */
public abstract class BaseEntity extends DataSimple {

	public abstract String getEntityIdentifier();

	public BaseEntity() {
		super();
		setIdentifier(getEntityIdentifier());
		setTenantId(SecurityUserUtil.getSecurityUserInfo().getTenantId());
		setCreateTime(System.currentTimeMillis());
		setCreateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
		setUpdateTime(System.currentTimeMillis());
		setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
	}

	public void update() {
		setUpdateTime(System.currentTimeMillis());
		setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
	}
}
