package com.caidaocloud.vms.domain.project.enums;

/**
 * 变更记录展示策略枚举
 * 
 * <AUTHOR>
 * @date 2025/10/10
 */
public enum ChangeDisplayStrategy {
    
    /**
     * 详细展示策略
     * 显示具体的字段变更，包括字段名、旧值、新值
     * 适用于：基本信息、设置等单一实体的变更
     */
    DETAILED("详细展示", "显示具体的字段变更信息"),
    
    /**
     * 简化展示策略
     * 只显示操作类型和实体描述，不显示具体字段变更
     * 适用于：供应商、联系人、岗位等集合类型的变更
     */
    SIMPLIFIED("简化展示", "只显示操作类型和实体描述");
    
    private final String display;
    private final String description;
    
    ChangeDisplayStrategy(String display, String description) {
        this.display = display;
        this.description = description;
    }
    
    public String getDisplay() {
        return display;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 判断是否为详细展示策略
     */
    public boolean isDetailed() {
        return this == DETAILED;
    }
    
    /**
     * 判断是否为简化展示策略
     */
    public boolean isSimplified() {
        return this == SIMPLIFIED;
    }
}
