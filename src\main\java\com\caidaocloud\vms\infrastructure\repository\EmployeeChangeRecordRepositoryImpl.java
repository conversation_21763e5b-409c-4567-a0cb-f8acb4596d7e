package com.caidaocloud.vms.infrastructure.repository;

import java.util.List;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.vms.application.dto.EmployeeChangeRecordQueryDTO;
import com.caidaocloud.vms.domain.base.repository.PostRepository;
import com.caidaocloud.vms.domain.employee.entity.EmployeeChangeRecord;
import com.caidaocloud.vms.domain.employee.repository.EmployeeChangeRecordRepository;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * 员工入离项记录Repository实现类
 * 
 * <AUTHOR>
 * @date 2025/10/29
 */
@Repository
public class EmployeeChangeRecordRepositoryImpl implements EmployeeChangeRecordRepository {
    @Autowired
    private PostRepository postRepository;

    @Override
    public void saveOrUpdate(EmployeeChangeRecord record) {
        if (record.getBid() == null) {
            DataInsert.identifier(EmployeeChangeRecord.identifier).insert(record);
        } else {
            DataUpdate.identifier(EmployeeChangeRecord.identifier).update(record);
        }
    }

    @Override
    public EmployeeChangeRecord getById(String recordId) {
        return DataQuery.identifier(EmployeeChangeRecord.identifier)
                .oneOrNull(recordId, EmployeeChangeRecord.class);
    }

    @Override
    public PageResult<EmployeeChangeRecord> findByPage(EmployeeChangeRecordQueryDTO queryDTO) {
        DataFilter filter = buildFilter(queryDTO);

        return DataQuery.identifier(EmployeeChangeRecord.identifier)
                .limit(queryDTO.getPageSize(), queryDTO.getPageNo())
                .filter(filter, EmployeeChangeRecord.class);
    }

    @Override
    public void deleteById(String recordId) {
        DataDelete.identifier(EmployeeChangeRecord.identifier).softDelete(recordId);
    }

    /**
     * 构建查询过滤条件
     *
     * @param queryDTO 查询条件
     * @return 过滤条件
     */
    private DataFilter buildFilter(EmployeeChangeRecordQueryDTO queryDTO) {
        DataFilter filter = DataFilter.ne("deleted", Boolean.TRUE.toString());

        // 项目ID过滤
        if (StringUtils.isNotEmpty(queryDTO.getProjectId())) {
            filter = filter.andEq("projectId", queryDTO.getProjectId());
        }

        // 公司ID过滤
        if (StringUtils.isNotEmpty(queryDTO.getCompanyId())) {
            filter = filter.andEq("companyId", queryDTO.getCompanyId());
        }

        // 供应商ID过滤
        if (StringUtils.isNotEmpty(queryDTO.getSupplierId())) {
            filter = filter.andEq("supplierId", queryDTO.getSupplierId());
        }

        if (StringUtils.isNotEmpty(queryDTO.getPositionName())) {
            List<String> postIds = postRepository.regexPostId(queryDTO.getPositionName());
            filter = filter.andIn("positionId", postIds);
        }

        // 员工姓名模糊查询
        if (StringUtils.isNotEmpty(queryDTO.getEmpName())) {
            filter = filter.andRegex("empName", queryDTO.getEmpName());
        }

        // 交接类型过滤
        if (queryDTO.getChangeType() != null) {
            filter = filter.andEq("type", String.valueOf(queryDTO.getChangeType()));
        }

        // 审批状态过滤
        if (queryDTO.getApprovalStatus() != null) {
            filter = filter.andEq("approvalStatus", String.valueOf(queryDTO.getApprovalStatus()));
        }

        return filter;
    }
}
