package com.caidaocloud.vms.domain.project.entity;

import com.caidaocloud.hrpaas.metadata.sdk.annotation.DisplayAsArray;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.vms.domain.base.entity.BaseEntity;
import com.caidaocloud.vms.domain.project.enums.HistoryType;
import com.caidaocloud.vms.domain.project.enums.OperationType;
import com.caidaocloud.vms.domain.project.history.DataSimpleHistoryFormat;
import com.caidaocloud.vms.domain.project.history.SimplifiedHistoryFormat;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Data
@Slf4j
public class ProjectHistoryDetail extends BaseEntity {

    private String historyId;

    private String projectId;

    private EnumSimple type;

    private String approveBy;

    private Long approveTime;

    private EnumSimple approveStatus;

    private String snapshot;

    @DisplayAsArray
    private List<ProjectChange> change;

    private String changeSummary;


    @DisplayAsArray
    private List<String> draft;

    public static String identifier = "entity.vms.ProjectHistoryDetail";

    @Override
    public String getEntityIdentifier() {
        return identifier;
    }

    protected ProjectHistoryDetail() {
    }

    public ProjectHistoryDetail(HistoryType type) {
        this.type = type.toEnumSimple();
    }

    public ProjectHistoryDetail(HistoryType type, String snapshot, List<ProjectChange> change) {
        this(type);
        this.snapshot = snapshot;
        this.change = change;
    }

    public ProjectHistoryDetail(HistoryType type, String snapshot, List<ProjectChange> change,List<String> draft) {
        this(type, snapshot, change);
        this.draft = draft;
    }

    public String generateChangeSummary(DataSimpleHistoryFormat entity, OperationType operation) {
        HistoryType historyType = HistoryType.fromValue(type.getValue());
        if (historyType == HistoryType.BASIC_INFO) {
            switch (operation) {
            case CREATE:
                return "项目创建";
            case UPDATE:
                return "项目修改";
            default:
                return "";
            }
        }
        if (historyType == HistoryType.SETTING) {
            StringBuilder sb = new StringBuilder();
            for (ProjectChange projectChange : change) {
                String fieldName = projectChange.getFieldName();
                Boolean b = Boolean.valueOf(projectChange.getNewValue().toString());
                sb.append(b ? "打开" : "关闭").append(fieldName).append(";");
            }
            return sb.toString();
        }

        String display = ((SimplifiedHistoryFormat) entity).formatDisplay();

        return String.format("%s: %s", operation.getDisplay(), display);
    }
}