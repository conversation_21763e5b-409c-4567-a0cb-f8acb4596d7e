package com.caidaocloud.vms.domain.project.repository;

import com.caidaocloud.vms.application.dto.PositionSupplierQueryDto;
import com.caidaocloud.vms.domain.project.entity.PositionSupplier;

import java.util.List;
import java.util.Optional;

public interface PositionSupplierRepository {

    /**
     * 保存或更新岗位供应商关系
     * 
     * @param positionSupplier 岗位供应商关系
     * @return 关系ID
     */
    String saveOrUpdate(PositionSupplier positionSupplier);

    /**
     * 根据岗位ID获取所有供应商关系
     * 
     * @param positionId 岗位ID
     * @return 供应商关系列表
     */
    List<PositionSupplier> getByPositionId(String positionId);

    /**
     * 根据查询条件获取岗位供应商关系列表
     * 
     * @param queryDto 查询条件
     * @return 供应商关系列表
     */
    List<PositionSupplier> getByQuery(PositionSupplierQueryDto queryDto);

    /**
     * 根据ID获取岗位供应商关系
     * 
     * @param relationId 关系ID
     * @return 岗位供应商关系
     */
    Optional<PositionSupplier> getById(String relationId);

    /**
     * 删除岗位供应商关系
     *
	 * @param positionSupplier 关系ID
	 */
    void deleteRelation(PositionSupplier positionSupplier);

    /**
     * 根据岗位ID删除所有供应商关系
     * 
     * @param positionId 岗位ID
     */
    void deleteByPositionId(String positionId);

    /**
     * 检查岗位和供应商的关系是否已存在
     * 
     * @param positionId 岗位ID
     * @param supplierId 供应商ID
     * @return 是否存在
     */
    boolean existsByPositionIdAndSupplierId(String positionId, String supplierId);

	List<PositionSupplier> listBySupplierId(String supplierId);

    List<PositionSupplier> listByProjectIdSupplierId(String projectId, String supplierId);

}
