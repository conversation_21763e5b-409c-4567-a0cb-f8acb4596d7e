package com.caidaocloud.vms.domain.project.repository;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.vms.application.dto.ProjectHistoryQueryDTO;
import com.caidaocloud.vms.domain.project.entity.ProjectHistoryDetail;

import java.util.List;
import java.util.Optional;

/**
 * 项目历史详情Repository接口
 * 
 * <AUTHOR>
 * @date 2025/9/27
 */
public interface ProjectHistoryDetailRepository {

    /**
     * 保存或更新项目历史详情
     * @param detail 项目历史详情
     * @return 详情ID
     */
    String saveOrUpdate(ProjectHistoryDetail detail);

    /**
     * 根据ID获取项目历史详情
     * @param detailId 详情ID
     * @return 项目历史详情
     */
    Optional<ProjectHistoryDetail> getById(String detailId);

    /**
     * 根据历史记录ID获取详情列表
     * @param historyId 历史记录ID
     * @return 详情列表
     */
    List<ProjectHistoryDetail> getByHistoryId(String historyId);

    /**
     * 删除项目历史详情
     * @param detailId 详情ID
     */
    void delete(String detailId);

	PageResult<ProjectHistoryDetail> findApprovedByPage(ProjectHistoryQueryDTO queryDTO);
}
