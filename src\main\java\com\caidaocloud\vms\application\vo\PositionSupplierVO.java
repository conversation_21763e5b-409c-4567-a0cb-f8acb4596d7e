package com.caidaocloud.vms.application.vo;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.vms.domain.project.enums.InviteStatus;
import com.caidaocloud.vms.domain.project.enums.PublishStatus;
import com.caidaocloud.vms.domain.project.enums.QuotationMode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel(description = "岗位供应商关系信息")
public class PositionSupplierVO {

    @ApiModelProperty(value = "关系ID")
    private String bid;

    @ApiModelProperty(value = "岗位ID")
    private String positionId;

    @ApiModelProperty(value = "供应商ID")
    private String supplierId;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "供应商联系人")
    private List<String> supplierContact;

    @ApiModelProperty("供应商联系人姓名")
    private String supplierContactName;

    @ApiModelProperty(value = "供应商联系人电话")
    @Deprecated
    private String supplierContactPhone;

    @ApiModelProperty(value = "供应商联系人邮箱")
    @Deprecated
    private String supplierContactEmail;

    @ApiModelProperty("发布状态")
    private PublishStatus publishStatus;

    @ApiModelProperty("提供人数")
    private Integer provide;
}
