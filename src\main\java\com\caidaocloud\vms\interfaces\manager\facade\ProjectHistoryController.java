package com.caidaocloud.vms.interfaces.manager.facade;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.vms.application.dto.ProjectHistoryQueryDTO;
import com.caidaocloud.vms.application.service.ProjectHistoryService;
import com.caidaocloud.vms.application.vo.ProjectHistoryDetailVO;
import com.caidaocloud.vms.application.vo.ProjectHistoryPageVO;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 项目历史变更记录控制器
 * 
 * <AUTHOR>
 * @date 2025/10/22
 */
@RestController
@RequestMapping("/api/vms/v1/manager/project/history")
@Api(tags = "项目历史变更记录管理", description = "项目历史变更记录的查询接口")
public class ProjectHistoryController {

    @Autowired
    private ProjectHistoryService projectHistoryService;

    /**
     * 分页查询审批通过的历史变更记录
     * 
     * @param queryDTO 查询条件
     * @return 历史记录列表
     */
    @PostMapping("/page")
    @ApiOperation(value = "分页查询历史变更记录", notes = "查询所有审批通过的历史变更记录，列表中展示变更类型、change的缩略信息、提交人、审批人、提交时间和审批时间")
    public Result<PageResult<ProjectHistoryPageVO>> getHistoryPage(
            @ApiParam(value = "查询条件", required = true) @RequestBody ProjectHistoryQueryDTO queryDTO) {

        PageResult<ProjectHistoryPageVO> result = projectHistoryService.findApprovedByPage(queryDTO);
        return Result.ok(result);
    }

    /**
     * 获取历史变更记录详情
     * 
     * @param historyId 历史记录ID
     * @return 历史记录详情
     */
    @GetMapping("/detail")
    @ApiOperation(value = "获取历史变更记录详情", notes = "展示变更后的数据snapshot和变更项change")
    public Result<ProjectHistoryDetailVO> getHistoryDetail(
            @ApiParam(value = "历史记录ID", required = true) @RequestParam String historyId) {

        ProjectHistoryDetailVO detail = projectHistoryService.getHistoryDetail(historyId);
        if (detail == null) {
            return Result.fail("历史记录不存在");
        }
        return Result.ok(detail);
    }
}
