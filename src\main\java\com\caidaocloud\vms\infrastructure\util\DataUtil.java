package com.caidaocloud.vms.infrastructure.util;

import java.util.HashMap;
import java.util.Map;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.PropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.dto.SimplePropertyValue;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.vms.application.dto.base.EmpInfoDto;

/**
 *
 * <AUTHOR>
 * @date 2025/10/15
 */
public class DataUtil {
	public static  <T> T convert(DataSimple data,Class<T> clazz){
		Map<String, Object> map = new HashMap<>();
		map.put("bid", data.getBid());
		for (Map.Entry<String, PropertyValue> entry : data
				.getProperties().entrySet()) {
			if (entry.getValue() instanceof SimplePropertyValue) {
				map.put(entry.getKey(), ((SimplePropertyValue) entry.getValue()).getValue());
			}
			else {
				map.put(entry.getKey(), entry.getValue());
			}
		}
		return FastjsonUtil.convertObject(map, clazz);
	}
}
