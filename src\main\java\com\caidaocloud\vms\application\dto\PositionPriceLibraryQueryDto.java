package com.caidaocloud.vms.application.dto;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.vms.domain.base.enums.ActiveStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 岗位价格库查询DTO
 *
 * <AUTHOR>
 * @date 2025/12/10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "岗位价格库查询DTO")
public class PositionPriceLibraryQueryDto extends BasePage {

    @ApiModelProperty(value = "岗位ID")
    private String postId;

    @ApiModelProperty("关键字")
    private String keywords;

    @ApiModelProperty(value = "组织ID")
    private String orgId;

    @ApiModelProperty(value = "状态")
    private ActiveStatus status;
}
