package com.caidaocloud.vms.domain.employee.entity;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.vms.domain.base.entity.BaseEntity;
import com.caidaocloud.vms.domain.base.enums.ApprovalStatus;
import com.caidaocloud.vms.domain.employee.enums.EmployeeChangeType;
import com.caidaocloud.vms.domain.project.enums.QuotationMode;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = false)
public class EmployeeChangeRecord extends BaseEntity {

    /**
     * 交接类型
     */
    private EmployeeChangeType type;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 供应商ID
     */
    private String supplierId;

    /**
     * 岗位ID
     */
    private String positionId;

    /**
     * 公司ID
     */
    private String companyId;

    /**
     * 员工ID
     */
    private String empId;

    /**
     * 员工姓名
     */
    private String empName;

    /**
     * 入项日期
     */
    private Long startTime;

    /**
     * 离项日期
     */
    private Long endTime;

    /**
     * 业务ID
     */
    private String businessId;

    /**
     * 薪资
     */
    private BigDecimal salary;

    /**
     * 收费模式
     */
    private QuotationMode quotationMode;

    private String quotationValue;

    /**
     * 审批状态
     */
    private ApprovalStatus approvalStatus;

    public static String identifier = "entity.vms.EmployeeChangeRecord";

    @Override
    public String getEntityIdentifier() {
        return identifier;
    }

    public void approved() {
        approvalStatus = ApprovalStatus.APPROVED;
        update();
    }

    public void rejected() {
        approvalStatus = ApprovalStatus.REJECTED;
        update();
    }
}