package com.caidaocloud.vms.domain.project.entity;

import com.caidaocloud.vms.domain.base.entity.BaseEntity;
import com.caidaocloud.vms.domain.base.enums.ActiveStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 岗位价格库实体类
 *
 * <AUTHOR>
 * @date 2025/12/10
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PositionPriceLibrary extends BaseEntity {

    /**
     * 岗位ID
     */
    private String postId;

    /**
     * 组织ID
     */
    private String orgId;

    /**
     * 最低薪资
     */
    private Integer minSalary;

    /**
     * 最高薪资
     */
    private Integer maxSalary;

    /**
     * 状态：启用/停用
     */
    private ActiveStatus status;

    public static String identifier = "entity.vms.PostPriceLibrary";

    @Override
    public String getEntityIdentifier() {
        return identifier;
    }

    /**
     * 创建岗位价格库
     */
    public static PositionPriceLibrary create(String postId, String orgId, Integer minSalary, Integer maxSalary) {
        PositionPriceLibrary library = new PositionPriceLibrary();
        library.setPostId(postId);
        library.setOrgId(orgId);
        library.setMinSalary(minSalary);
        library.setMaxSalary(maxSalary);
        library.setStatus(ActiveStatus.ACTIVE); // 默认启用
        return library;
    }

    /**
     * 更新薪资范围
     */
    public void updateSalaryRange(Integer minSalary, Integer maxSalary) {
        this.minSalary = minSalary;
        this.maxSalary = maxSalary;
        this.update(); // 更新修改时间和修改人
    }

    /**
     * 启用
     */
    public void enable() {
        this.status = ActiveStatus.ACTIVE;
        this.update();
    }

    /**
     * 停用
     */
    public void disable() {
        this.status = ActiveStatus.INACTIVE;
        this.update();
    }
}
