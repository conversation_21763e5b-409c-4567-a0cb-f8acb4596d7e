package com.caidaocloud.vms.infrastructure.repository;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.vms.domain.base.enums.ActiveStatus;
import com.caidaocloud.vms.application.dto.PositionPriceLibraryQueryDto;
import com.caidaocloud.vms.domain.base.repository.PostRepository;
import com.caidaocloud.vms.domain.project.entity.PositionPriceLibrary;
import com.caidaocloud.vms.domain.project.repository.PositionPriceLibraryRepository;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 岗位价格库Repository实现
 *
 * <AUTHOR> Zhou
 * @date 2025/12/10
 */
@Repository
public class PositionPriceLibraryRepositoryImpl implements PositionPriceLibraryRepository {
    @Autowired
    private PostRepository postRepository;

    @Override
    public String saveOrUpdate(PositionPriceLibrary library) {
        if (library.getBid() == null) {
            DataInsert.identifier(PositionPriceLibrary.identifier).insert(library);
        } else {
            DataUpdate.identifier(PositionPriceLibrary.identifier).update(library);
        }
        return library.getBid();
    }

    @Override
    public Optional<PositionPriceLibrary> getById(String bid) {
        return Optional.ofNullable(
                DataQuery.identifier(PositionPriceLibrary.identifier)
                        .oneOrNull(bid, PositionPriceLibrary.class));
    }

    @Override
    public PageResult<PositionPriceLibrary> findByPage(PositionPriceLibraryQueryDto queryDto) {
        DataFilter filter = buildFilter(queryDto);
        if (StringUtils.isNotEmpty(queryDto.getKeywords())) {
            List<String> postIds = postRepository.regexPostId(queryDto.getKeywords());
            filter = filter.andIn("postId", postIds);
        }
        return DataQuery.identifier(PositionPriceLibrary.identifier)
                .limit(queryDto.getPageSize(), queryDto.getPageNo())
                .filter(filter, PositionPriceLibrary.class);
    }

    @Override
    public boolean existsByPostIdAndOrgId(String postId, String orgId, String excludeBid) {
        DataFilter filter = DataFilter.ne("deleted", Boolean.TRUE.toString())
                .andEq("postId", postId)
                .andEq("orgId", orgId);

        if (StringUtils.isNotBlank(excludeBid)) {
            filter = filter.andNe("bid", excludeBid);
        }

        PageResult<PositionPriceLibrary> result = DataQuery.identifier(PositionPriceLibrary.identifier)
                .limit(1, 1)
                .filter(filter, PositionPriceLibrary.class);

        return result.getTotal() > 0;
    }

    /**
     * 构建查询过滤条件
     */
    private DataFilter buildFilter(PositionPriceLibraryQueryDto queryDto) {
        DataFilter filter = DataFilter.ne("deleted", Boolean.TRUE.toString());

        if (StringUtils.isNotBlank(queryDto.getPostId())) {
            filter = filter.andEq("postId", queryDto.getPostId());
        }

        if (StringUtils.isNotBlank(queryDto.getOrgId())) {
            filter = filter.andEq("orgId", queryDto.getOrgId());
        }

        if (queryDto.getStatus() != null) {
            filter = filter.andEq("status", String.valueOf(queryDto.getStatus().getCode()));
        }

        return filter;
    }

    @Override
    public Optional<PositionPriceLibrary> findByPostIdAndOrgId(String postId, String orgId) {
        if (StringUtils.isBlank(postId) || StringUtils.isBlank(orgId)) {
            return Optional.empty();
        }

        DataFilter filter = DataFilter.ne("deleted", Boolean.TRUE.toString())
                .andEq("postId", postId)
                .andEq("orgId", orgId)
                .andEq("status", String.valueOf(ActiveStatus.ACTIVE.getCode())); // 只查找启用状态的记录

        List<PositionPriceLibrary> results = DataQuery.identifier(PositionPriceLibrary.identifier)
                .limit(1, 1)
                .filter(filter, PositionPriceLibrary.class)
                .getItems();

        return results.isEmpty() ? Optional.empty() : Optional.of(results.get(0));
    }

    @Override
    public void delete(PositionPriceLibrary library) {
        DataDelete.identifier(PositionPriceLibrary.identifier).delete(library.getBid());
    }
}
