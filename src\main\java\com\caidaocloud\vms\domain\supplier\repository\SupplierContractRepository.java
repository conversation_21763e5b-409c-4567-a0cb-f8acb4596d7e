package com.caidaocloud.vms.domain.supplier.repository;


import java.util.List;
import java.util.Optional;

import com.caidaocloud.vms.domain.supplier.entity.SupplierContract;

public interface SupplierContractRepository {


	String saveOrUpdate(SupplierContract supplierContract);

	void deleteContract(String contractId);

	Optional<SupplierContract> getContract(String bid);

	List<SupplierContract> listContract(String supplierId);
}