package com.caidaocloud.vms.application.service;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.annotation.PaasTransactional;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.vms.application.dto.PositionPriceLibraryCreateDto;
import com.caidaocloud.vms.application.dto.PositionPriceLibraryQueryDto;
import com.caidaocloud.vms.application.dto.PositionPriceLibrarySalaryRangeDto;
import com.caidaocloud.vms.application.dto.PositionPriceLibraryUpdateDto;
import com.caidaocloud.vms.application.dto.base.OrgInfoDto;
import com.caidaocloud.vms.application.dto.base.PostInfoDto;
import com.caidaocloud.vms.application.service.emp.OrganizeService;
import com.caidaocloud.vms.application.service.emp.PostService;
import com.caidaocloud.vms.application.vo.PositionPriceLibraryVO;
import com.caidaocloud.vms.domain.project.entity.PositionPriceLibrary;
import com.caidaocloud.vms.domain.project.repository.PositionPriceLibraryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 岗位价格库服务类
 *
 * <AUTHOR> Zhou
 * @date 2025/12/10
 */
@Service
public class PositionPriceLibraryService {

    @Autowired
    private PositionPriceLibraryRepository positionPriceLibraryRepository;

    @Autowired
    private PostService postService;

    @Autowired
    private OrganizeService organizeService;

    /**
     * 新建岗位价格库
     *
     * @param createDto 创建DTO
     */
    @PaasTransactional
    public void create(PositionPriceLibraryCreateDto createDto) {
        // 校验参数
        validateSalaryRange(createDto.getMinSalary(), createDto.getMaxSalary());

        // 检查是否已存在相同岗位和组织的价格库
        if (positionPriceLibraryRepository.existsByPostIdAndOrgId(
                createDto.getPostId(), createDto.getOrgId(), null)) {
            throw new ServerException("该岗位在此组织下的价格库已存在");
        }

        // 创建实体
        PositionPriceLibrary library = PositionPriceLibrary.create(
                createDto.getPostId(),
                createDto.getOrgId(),
                createDto.getMinSalary(),
                createDto.getMaxSalary());

        // 保存
        positionPriceLibraryRepository.saveOrUpdate(library);
    }

    /**
     * 根据主键修改薪资范围
     *
     * @param updateDto 更新DTO
     */
    @PaasTransactional
    public void updateSalaryRange(PositionPriceLibraryUpdateDto updateDto) {
        // 校验参数
        validateSalaryRange(updateDto.getMinSalary(), updateDto.getMaxSalary());

        // 获取实体
        Optional<PositionPriceLibrary> libraryOpt = positionPriceLibraryRepository.getById(updateDto.getBid());
        if (!libraryOpt.isPresent()) {
            throw new ServerException("岗位价格库不存在");
        }

        PositionPriceLibrary library = libraryOpt.get();

        // 更新薪资范围
        library.updateSalaryRange(updateDto.getMinSalary(), updateDto.getMaxSalary());

        // 保存
        positionPriceLibraryRepository.saveOrUpdate(library);
    }

    /**
     * 分页查询价格库
     *
     * @param queryDto 查询条件
     * @return 分页结果
     */
    public PageResult<PositionPriceLibraryVO> findByPage(PositionPriceLibraryQueryDto queryDto) {
        // 分页查询
        PageResult<PositionPriceLibrary> pageResult = positionPriceLibraryRepository.findByPage(queryDto);

        // 转换为VO
        List<PositionPriceLibraryVO> voList = convertToVOList(pageResult.getItems());

        return new PageResult<>(voList, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal());
    }

    /**
     * 启用价格库
     *
     * @param bid 主键ID
     */
    @PaasTransactional
    public void enable(String bid) {
        Optional<PositionPriceLibrary> libraryOpt = positionPriceLibraryRepository.getById(bid);
        if (!libraryOpt.isPresent()) {
            throw new ServerException("岗位价格库不存在");
        }

        PositionPriceLibrary library = libraryOpt.get();
        library.enable();
        positionPriceLibraryRepository.saveOrUpdate(library);
    }

    /**
     * 停用价格库
     *
     * @param bid 主键ID
     */
    @PaasTransactional
    public void disable(String bid) {
        Optional<PositionPriceLibrary> libraryOpt = positionPriceLibraryRepository.getById(bid);
        if (!libraryOpt.isPresent()) {
            throw new ServerException("岗位价格库不存在");
        }

        PositionPriceLibrary library = libraryOpt.get();
        library.disable();
        positionPriceLibraryRepository.saveOrUpdate(library);
    }

    /**
     * 校验薪资范围
     */
    private void validateSalaryRange(Integer minSalary, Integer maxSalary) {
        if (minSalary > maxSalary) {
            throw new ServerException("最低薪资不能大于最高薪资");
        }
    }

    /**
     * 转换为VO列表
     */
    private List<PositionPriceLibraryVO> convertToVOList(List<PositionPriceLibrary> libraries) {
        if (libraries.isEmpty()) {
            return new ArrayList<>();
        }

        // 收集岗位ID和组织ID
        List<String> postIds = libraries.stream()
                .map(PositionPriceLibrary::getPostId)
                .distinct()
                .collect(Collectors.toList());

        List<String> orgIds = libraries.stream()
                .map(PositionPriceLibrary::getOrgId)
                .distinct()
                .collect(Collectors.toList());

        // 批量查询岗位和组织信息
        Map<String, PostInfoDto> postMap = postService.loadPostList(postIds).stream()
                .collect(Collectors.toMap(PostInfoDto::getBid, post -> post));

        Map<String, OrgInfoDto> orgMap = organizeService.loadOrgList(orgIds).stream()
                .collect(Collectors.toMap(OrgInfoDto::getBid, org -> org));

        // 转换为VO
        return libraries.stream().map(library -> {
            PositionPriceLibraryVO vo = ObjectConverter.convert(library, PositionPriceLibraryVO.class);

            // 设置岗位名称
            PostInfoDto post = postMap.get(library.getPostId());
            if (post != null) {
                vo.setPostName(post.getName());
                vo.setJobGrade(post.getJobGrade());
            }

            // 设置组织名称
            OrgInfoDto org = orgMap.get(library.getOrgId());
            if (org != null) {
                vo.setOrgName(org.getOrgName());
            }

            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 根据岗位ID和组织ID查找薪资范围
     *
     * @param postId 岗位ID
     * @param orgId  组织ID
     * @return 薪资范围信息
     */
    public PositionPriceLibrarySalaryRangeDto findSalaryRangeByPostIdAndOrgId(String postId, String orgId) {
        // 参数校验
        if (postId == null || postId.trim().isEmpty()) {
            throw new ServerException("岗位ID不能为空");
        }
        if (orgId == null || orgId.trim().isEmpty()) {
            throw new ServerException("组织ID不能为空");
        }

        // 查找价格库记录
        Optional<PositionPriceLibrary> libraryOpt = positionPriceLibraryRepository.findByPostIdAndOrgId(postId, orgId);

        if (!libraryOpt.isPresent()) {
            // 未找到记录，返回空结果
            PositionPriceLibrarySalaryRangeDto result = PositionPriceLibrarySalaryRangeDto.notFound(postId, orgId);
            return result;
        }

        // 找到记录，返回薪资范围
        PositionPriceLibrary library = libraryOpt.get();
        PositionPriceLibrarySalaryRangeDto result = PositionPriceLibrarySalaryRangeDto.found(
                postId, orgId, library.getMinSalary(), library.getMaxSalary());

        return result;
    }

  public   void delete(String bid) {
      Optional<PositionPriceLibrary> libraryOpt = positionPriceLibraryRepository.getById(bid);
      if (!libraryOpt.isPresent()) {
          throw new ServerException("岗位价格库不存在");
      }

      PositionPriceLibrary library = libraryOpt.get();
      positionPriceLibraryRepository.delete(library);
    }
}
