package com.caidaocloud.vms.application.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(description = "项目岗位信息")
public class ProjectPositionDto {

    @ApiModelProperty(value = "岗位ID")
    private String bid;

    @ApiModelProperty(value = "项目ID", required = true)
    private String projectId;

    @ApiModelProperty(value = "公司ID", required = true)
    private String company;

    @ApiModelProperty(value = "组织ID", required = true)
    private String organization;

    @ApiModelProperty(value = "岗位ID", required = true)
    private String position;

    @ApiModelProperty(value = "开始日期(时间戳)")
    private Long startDate;

    @ApiModelProperty(value = "结束日期(时间戳)")
    private Long endDate;

    @ApiModelProperty(value = "持续时间类型")
    private String durationType;

    @ApiModelProperty(value = "持续时间(天)")
    private Integer duration;

    @ApiModelProperty(value = "总预算")
    private Integer totalBudget;

    @ApiModelProperty(value = "已用预算")
    private Integer usedBudget;

    @ApiModelProperty(value = "计划人数")
    private Integer plannedHeadcount;

    @ApiModelProperty(value = "实际人数")
    private Integer actualHeadcount;

    @ApiModelProperty(value = "工作地点")
    private String workplace;

    @ApiModelProperty(value = "联系人员工ID")
    private String contactEmpId;

    @ApiModelProperty(value = "用工类型")
    private String employmentType;

    @ApiModelProperty(value = "审批状态")
    private String approveStatus;
}
