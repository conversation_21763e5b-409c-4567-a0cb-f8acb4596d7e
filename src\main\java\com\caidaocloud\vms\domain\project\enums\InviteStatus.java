package com.caidaocloud.vms.domain.project.enums;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import org.apache.commons.lang3.StringUtils;

public enum InviteStatus {
    INITIATED(0, "发起"),
    AGREED(1, "同意"),
    REJECTED(2, "拒绝"),
    TERMINATED(3, "终止");

    private final int value;
    private final String display;

    InviteStatus(int value, String display) {
        this.value = value;
        this.display = display;
    }

    public int getValue() {
        return value;
    }

    public String getDisplay() {
        return display;
    }

    public static InviteStatus fromValue(int value) {
        for (InviteStatus status : InviteStatus.values()) {
            if (status.value == value) {
                return status;
            }
        }
        throw new ServerException("Unknown value: " + value);
    }

    public static InviteStatus fromValue(EnumSimple value) {
        if (value == null || StringUtils.isEmpty(value.getValue())) {
            return null;
        }
        int v = Integer.parseInt(value.getValue());
        for (InviteStatus status : InviteStatus.values()) {
            if (status.value == v) {
                return status;
            }
        }
        return null;
    }


    public EnumSimple toEnumSimple() {
        EnumSimple enumSimple = new EnumSimple();
        enumSimple.setValue(String.valueOf(value));
        return enumSimple;
    }
}