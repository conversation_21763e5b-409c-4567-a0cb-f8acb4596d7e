package com.caidaocloud.vms.domain.project.enums;

import java.util.List;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.vms.domain.project.entity.ProjectChange;
import com.caidaocloud.vms.domain.project.entity.ProjectContact;
import com.caidaocloud.vms.domain.project.entity.ProjectSupplier;
import com.caidaocloud.vms.domain.project.history.DataSimpleHistoryFormat;
import com.googlecode.totallylazy.Sequences;

public enum HistoryType {
    BASIC_INFO("0", "基本信息", "BASIC_INFO"){
        @Override
        public String generateChangeSummary(OperationType type, DataSimpleHistoryFormat data) {
                switch (type){
                case CREATE:
                    return "项目创建";
                case UPDATE:
                    return "项目修改";
                default:
                    return "";
                }
        }
        // @Override
        // String generateChangeSummary(OperationType operation,DataSimpleHistoryFormat data) {

        // }
    },
    SUPPLIER("1", "供应商", "SUPPLIER"){
        @Override
        public String generateChangeSummary(OperationType type, DataSimpleHistoryFormat data) {
            return ((ProjectSupplier) data).formatDisplay();
        }
    },
    CONTACT("2", "联系人", "CONTACT") {
        @Override
        public String generateChangeSummary(OperationType type, DataSimpleHistoryFormat data) {
            return ((ProjectContact) data).formatDisplay();
        }
    },
    POSITION("3", "岗位", "POSITION") {
        @Override
        public String generateChangeSummary(OperationType type, DataSimpleHistoryFormat data) {
            return null;
        }
    },
    SETTING("4", "设置", "SETTING") {
        @Override
        public String generateChangeSummary(OperationType type, DataSimpleHistoryFormat data) {
            return null;
        }
    },


    REQUIRE("10", "招聘要求", "REQUIREMENT") {
        @Override
        public String generateChangeSummary(OperationType type, DataSimpleHistoryFormat data) {
            return null;
        }
    },

    NONE("-1","","") {
        @Override
        public String generateChangeSummary(OperationType type, DataSimpleHistoryFormat data) {
            return null;
        }
    };

    private final String value;
    private final String display;
    private final String code;

    HistoryType(String value, String display, String code) {
        this.value = value;
        this.display = display;
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public String getDisplay() {
        return display;
    }

    public String getCode() {
        return code;
    }

    // 根据value查找枚举
    public static HistoryType fromValue(String value) {
        for (HistoryType type : values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        throw new ServerException("无效的页面类型值: " + value);
    }

    // 根据display查找枚举
    public static HistoryType fromDisplay(String display) {
        for (HistoryType type : values()) {
            if (type.display.equals(display)) {
                return type;
            }
        }
        throw new ServerException("无效的页面类型显示名称: " + display);
    }

    public EnumSimple toEnumSimple() {
        EnumSimple enumSimple = new EnumSimple();
        enumSimple.setValue(value);
        return enumSimple;
    }

    public boolean isArray() {
        return this == SUPPLIER || this == CONTACT || this == POSITION;
    }

    @Deprecated
    public  abstract String generateChangeSummary(OperationType type,DataSimpleHistoryFormat data);
}