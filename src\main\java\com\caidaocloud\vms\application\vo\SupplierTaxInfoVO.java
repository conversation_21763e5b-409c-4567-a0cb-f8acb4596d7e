package com.caidaocloud.vms.application.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@ApiModel(description = "供应商税务信息 VO")
@Data
public class SupplierTaxInfoVO {

    @ApiModelProperty(value = "业务ID")
    private String bid;

    @ApiModelProperty(value = "供应商ID")
    private String supplierId;

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "营业执照注册号")
    private String businessLicense;

    @ApiModelProperty(value = "纳税人识别号")
    private String taxId;

    @ApiModelProperty(value = "注册电话")
    private String phone;

    @ApiModelProperty(value = "注册地址")
    private String address;

    @ApiModelProperty(value = "开户银行")
    private String bankName;

    @ApiModelProperty(value = "银行账户")
    private String bankAccount;
}