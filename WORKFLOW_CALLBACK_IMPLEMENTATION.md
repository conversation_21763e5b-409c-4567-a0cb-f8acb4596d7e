# 工作流回调接口实现文档

## 概述

根据用户需求，我们完成了以下两个主要任务：

1. **删除 cancel API** - 从工作流配置中移除了 CANCEL 回调
2. **实现 callback 接口** - 实现了工作流审批回调处理逻辑

## 实现内容

### 1. 删除 Cancel API

**修改文件**: `src\main\java\com\caidaocloud\vms\domain\project\enums\WorkflowConfig.java`

- 从 `PROJECT_MANAGEMENT` 配置中移除了 CANCEL 回调
- 从 `PROJECT_POSITION` 配置中移除了 CANCEL 回调
- 现在每个工作流只包含 APPROVE（通过）和 REJECT（拒绝）两种回调

**当前回调配置**:
```java
PROJECT_MANAGEMENT(
    "项目管理", 
    "VMS_PROJECT",
    "/api/vms/v1/project/detail",
    new CallbackConfig[] {
        new CallbackConfig("APPROVE", "通过", "/api/vms/v1/project/workflow/approve"),
        new CallbackConfig("REJECT", "拒绝", "/api/vms/v1/project/workflow/reject")
    }),

PROJECT_POSITION(
    "项目管理-岗位", 
    "VMS_POSITION",
    "/api/vms/v1/project/detail",
    new CallbackConfig[] {
        new CallbackConfig("APPROVE", "通过", "/api/vms/v1/project/position/workflow/approve"),
        new CallbackConfig("REJECT", "拒绝", "/api/vms/v1/project/position/workflow/reject")
    });
```

### 2. 实现 Callback 接口

#### 2.1 创建工作流回调控制器

**新增文件**: `src\main\java\com\caidaocloud\vms\interfaces\facade\WorkflowCallbackController.java`

提供以下 REST API 端点：

- `POST /api/vms/v1/project/workflow/approve` - 项目工作流审批通过
- `POST /api/vms/v1/project/workflow/reject` - 项目工作流审批拒绝
- `POST /api/vms/v1/project/position/workflow/approve` - 项目岗位工作流审批通过
- `POST /api/vms/v1/project/position/workflow/reject` - 项目岗位工作流审批拒绝

所有端点都接受 `businessId`（业务单据ID，即历史记录ID）参数。

#### 2.2 创建工作流回调服务

**新增文件**: `src\main\java\com\caidaocloud\vms\application\service\WorkflowCallbackService.java`

实现核心业务逻辑：

**审批通过逻辑**:
1. 根据 `businessId` 获取 `ProjectHistory` 记录
2. 获取项目相关的所有 `ProjectDraft` 草稿数据
3. 按草稿类型反序列化并保存到对应的模型：
   - `BASIC_INFO` → `Project` 实体
   - `POSITION` → `ProjectPosition` 实体
   - `CONTACT` → `ProjectContact` 实体
   - `SUPPLIER` → `ProjectSupplier` 实体
4. 清理草稿数据

**审批拒绝逻辑**:
1. 根据 `businessId` 获取 `ProjectHistory` 记录
2. 获取项目相关的所有 `ProjectDraft` 草稿数据
3. 对于 `CREATE` 操作的草稿，删除对应的模型数据：
   - `POSITION` → 删除 `ProjectPosition` 实体
   - `CONTACT` → 删除 `ProjectContact` 实体
   - `SUPPLIER` → 删除 `ProjectSupplier` 实体
4. 对于 `UPDATE` 操作的草稿，只删除草稿数据，保留原始数据
5. 清理草稿数据

#### 2.3 扩展 Repository 接口

**修改文件**: `src\main\java\com\caidaocloud\vms\domain\project\repository\ProjectSupplierRepository.java`

新增方法：
- `Optional<ProjectSupplier> getSupplier(String supplierId)` - 根据ID获取项目供应商
- `void deleteSupplier(ProjectSupplier supplier)` - 删除项目供应商

**修改文件**: `src\main\java\com\caidaocloud\vms\infrastructure\repository\ProjectSupplierRepositoryImpl.java`

实现新增的接口方法。

## 技术特点

### 1. 事务管理
- 使用 `@PaasTransactional` 注解确保数据一致性
- 所有回调操作都在事务中执行

### 2. 错误处理
- 完善的异常处理和日志记录
- 业务异常使用 `ServerException` 包装
- 详细的操作日志便于问题排查

### 3. 数据反序列化
- 使用 `FastjsonUtil.toObject()` 从草稿的 `snapshot` 字段反序列化实体
- 支持多种实体类型的自动识别和处理

### 4. 操作类型区分
- 根据 `OperationType` 区分 CREATE、UPDATE、DELETE 操作
- 拒绝时只对 CREATE 操作删除模型数据，UPDATE 操作保留原始数据

## API 使用示例

### 审批通过
```bash
curl -X POST "http://localhost:8080/api/vms/v1/project/workflow/approve" \
     -H "Content-Type: application/json" \
     -d "businessId=history-123"
```

### 审批拒绝
```bash
curl -X POST "http://localhost:8080/api/vms/v1/project/workflow/reject" \
     -H "Content-Type: application/json" \
     -d "businessId=history-123"
```

## 测试

创建了完整的单元测试：
- `src\test\java\com\caidaocloud\vms\application\service\WorkflowCallbackServiceTest.java`

测试覆盖：
- 审批通过场景
- 审批拒绝场景（CREATE 和 UPDATE 操作）
- 异常处理场景
- 边界条件测试

## 编译状态

✅ **编译成功** - 所有代码已通过 Maven 编译验证

## 总结

本次实现完全满足用户需求：

1. ✅ **删除了 cancel API** - 工作流配置中不再包含 CANCEL 回调
2. ✅ **实现了 callback 接口** - 完整的审批通过和拒绝处理逻辑
3. ✅ **审批通过时将草稿保存到对应模型** - 支持多种实体类型
4. ✅ **审批拒绝时删除 CREATE 操作的模型数据** - 区分操作类型进行处理

代码结构清晰，错误处理完善，具有良好的可维护性和扩展性。
