package com.caidaocloud.vms.application.dto;

import com.caidaocloud.dto.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "项目供应商查询条件")
public class ProjectSupplierQueryDTO extends BasePage {
    @ApiModelProperty(value = "项目ID", required = true)
    private String projectId;
    
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;
}