package com.caidaocloud.vms.infrastructure.repository.base;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.vms.application.dto.base.CompanyInfoDto;
import com.caidaocloud.vms.domain.base.repository.CompanyRepository;
import com.caidaocloud.vms.infrastructure.util.DataUtil;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Sequences;
import org.apache.commons.collections.CollectionUtils;

import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR>
 * @date 2025/10/15
 */
@Repository
public class CompanyRepositoryImpl implements CompanyRepository {

	@Override
	public List<CompanyInfoDto> loadCompanyList(List<String> companyIds) {
		companyIds = Sequences.sequence(companyIds).filter(Objects::nonNull).toList();
		if (CollectionUtils.isEmpty(companyIds)) {
			return new ArrayList<>();
		}
		List<DataSimple> list = DataQuery.identifier("entity.hr.Company").limit(-1, 1)
				.filter(DataFilter.in("bid", companyIds).andNe("deleted", Boolean.TRUE.toString()), DataSimple.class)
				.getItems();

		return Sequences.sequence(list).map(data -> DataUtil.convert(data, CompanyInfoDto.class)).toList();
	}

	@Override
	public List<String> regexPostIdByName(String name) {
		List<Map<String, String>> regex = DataQuery.identifier("entity.hr.Company").limit(-1, 1)
				.filterProperties(DataFilter.regex("name", name)
						.andNe("deleted", Boolean.TRUE.toString()), Lists.list("bid"), System.currentTimeMillis())
				.getItems();

		return Sequences.sequence(regex).map(map -> map.get("bid")).toList();
	}
}
