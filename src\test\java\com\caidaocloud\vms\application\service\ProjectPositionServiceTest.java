// package com.caidaocloud.vms.application.service;
//
// import com.caidaocloud.exception.ServerException;
// import com.caidaocloud.vms.application.dto.PositionSupplierDto;
// import com.caidaocloud.vms.domain.project.entity.PositionSupplier;
// import com.caidaocloud.vms.domain.project.entity.ProjectPosition;
// import com.caidaocloud.vms.domain.project.repository.PositionSupplierRepository;
// import com.caidaocloud.vms.domain.project.repository.ProjectPositionRepository;
// import org.junit.jupiter.api.BeforeEach;
// import org.junit.jupiter.api.Test;
// import org.junit.jupiter.api.extension.ExtendWith;
// import org.mockito.InjectMocks;
// import org.mockito.Mock;
// import org.mockito.junit.jupiter.MockitoExtension;
//
// import java.util.Arrays;
// import java.util.List;
// import java.util.Optional;
//
// import static org.junit.jupiter.api.Assertions.*;
// import static org.mockito.ArgumentMatchers.anyString;
// import static org.mockito.Mockito.*;
//
// @ExtendWith(MockitoExtension.class)
// class ProjectPositionServiceTest {
//
//     @Mock
//     private ProjectPositionRepository projectPositionRepository;
//
//     @Mock
//     private PositionSupplierRepository positionSupplierRepository;
//
//     @InjectMocks
//     private ProjectPositionService projectPositionService;
//
//     private ProjectPosition mockPosition;
//     private PositionSupplier mockSupplier1;
//     private PositionSupplier mockSupplier2;
//
//     @BeforeEach
//     void setUp() {
//         // 创建模拟岗位，计划人数为10
//         mockPosition = new ProjectPosition();
//         mockPosition.setBid("position-001");
//         mockPosition.setPlannedHeadcount(10);
//
//         // 创建模拟供应商1，提供人数为5
//         mockSupplier1 = new PositionSupplier();
//         mockSupplier1.setBid("supplier-001");
//         mockSupplier1.setPositionId("position-001");
//         mockSupplier1.setProvide(5);
//
//         // 创建模拟供应商2，提供人数为3
//         mockSupplier2 = new PositionSupplier();
//         mockSupplier2.setBid("supplier-002");
//         mockSupplier2.setPositionId("position-001");
//         mockSupplier2.setProvide(3);
//     }
//
//     @Test
//     void testEditPositionSupplierContact_Success() {
//         // 准备测试数据
//         PositionSupplierDto dto = new PositionSupplierDto();
//         dto.setBid("supplier-001");
//         dto.setProvide(4); // 将提供人数从5改为4
//
//         // 模拟repository返回
//         when(positionSupplierRepository.getById("supplier-001")).thenReturn(Optional.of(mockSupplier1));
//         when(projectPositionRepository.getPosition("position-001")).thenReturn(Optional.of(mockPosition));
//         when(positionSupplierRepository.getByPositionId("position-001"))
//                 .thenReturn(Arrays.asList(mockSupplier1, mockSupplier2));
//
//         // 执行测试
//         assertDoesNotThrow(() -> projectPositionService.editPositionSupplierContact(dto));
//
//         // 验证保存方法被调用
//         verify(positionSupplierRepository).saveOrUpdate(mockSupplier1);
//     }
//
//     @Test
//     void testEditPositionSupplierContact_ExceedsPlannedHeadcount() {
//         // 准备测试数据
//         PositionSupplierDto dto = new PositionSupplierDto();
//         dto.setBid("supplier-001");
//         dto.setProvide(8); // 将提供人数从5改为8，总数将变为11，超过计划人数10
//
//         // 模拟repository返回
//         when(positionSupplierRepository.getById("supplier-001")).thenReturn(Optional.of(mockSupplier1));
//         when(projectPositionRepository.getPosition("position-001")).thenReturn(Optional.of(mockPosition));
//         when(positionSupplierRepository.getByPositionId("position-001"))
//                 .thenReturn(Arrays.asList(mockSupplier1, mockSupplier2));
//
//         // 执行测试并验证异常
//         ServerException exception = assertThrows(ServerException.class,
//                 () -> projectPositionService.editPositionSupplierContact(dto));
//
//         assertTrue(exception.getMessage().contains("所有供应商提供人数总和(11)不能超过岗位计划人数(10)"));
//     }
//
//     @Test
//     void testEditPositionSupplierContact_PositionNotFound() {
//         // 准备测试数据
//         PositionSupplierDto dto = new PositionSupplierDto();
//         dto.setBid("supplier-001");
//         dto.setProvide(4);
//
//         // 模拟repository返回
//         when(positionSupplierRepository.getById("supplier-001")).thenReturn(Optional.of(mockSupplier1));
//         when(projectPositionRepository.getPosition("position-001")).thenReturn(Optional.empty());
//
//         // 执行测试并验证异常
//         ServerException exception = assertThrows(ServerException.class,
//                 () -> projectPositionService.editPositionSupplierContact(dto));
//
//         assertEquals("岗位不存在", exception.getMessage());
//     }
//
//     @Test
//     void testEditPositionSupplierContact_RelationNotFound() {
//         // 准备测试数据
//         PositionSupplierDto dto = new PositionSupplierDto();
//         dto.setBid("non-existent-supplier");
//         dto.setProvide(4);
//
//         // 模拟repository返回
//         when(positionSupplierRepository.getById("non-existent-supplier")).thenReturn(Optional.empty());
//
//         // 执行测试并验证异常
//         ServerException exception = assertThrows(ServerException.class,
//                 () -> projectPositionService.editPositionSupplierContact(dto));
//
//         assertEquals("岗位供应商关系不存在", exception.getMessage());
//     }
// }
