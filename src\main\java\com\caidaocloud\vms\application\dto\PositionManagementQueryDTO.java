package com.caidaocloud.vms.application.dto;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.vms.domain.project.enums.PositionStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "岗位管理查询条件")
public class PositionManagementQueryDTO extends BasePage {
    
    @ApiModelProperty(value = "项目ID")
    private String projectId;
    
    // @ApiModelProperty(value = "项目名称（模糊查询）")
    // private String projectName;
    
    @ApiModelProperty(value = "岗位名称（模糊查询）")
    private String positionName;
    //
    // @ApiModelProperty(value = "岗位编码（模糊查询）")
    // private String positionCode;
    @ApiModelProperty(value = "公司id")
    private String companyId;

    @ApiModelProperty(value = "公司名称（模糊查询）")
    private String companyName;

    @ApiModelProperty(value = "状态")
    private PositionStatus approveStatus;

    @ApiModelProperty(value = "供应商ID（供应商查询时使用）", hidden = true)
    private String supplierId;
}
