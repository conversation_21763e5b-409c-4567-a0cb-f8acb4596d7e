package com.caidaocloud.vms.domain.employee.entity.onboarding;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.vms.domain.base.entity.BaseEntity;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class OnboardingEmpPrivateInfo extends BaseEntity {

    private String empId;

    private String name;

    private String enName;

    private DictSimple sex;

    private DictSimple nationality;

    private DictSimple nation;

    private String nativePlace;

    private DictSimple familyType;

    private String permanentAddress;

    private Long birthDate;

    private Integer divisionAge;

    private EnumSimple maritalStatus;

    private EnumSimple fertilityStatus;

    private DictSimple politicalOutlook;

    private String phone;

    private String email;

    private String postalAddress;

    private EnumSimple cardType;

    private String cardNo;

    private Long cardEffectiveDate;

    private Boolean disability = false;

    private String guardianName;

    private String guardianPhone;

    private String guardianEmail;

    private Long workingStartDate;

    private Boolean adult = false;

    private Boolean criminal = false;

    private String crimeRecord;

    private String permanentProvince;

    private String familyProvince;

    public static String identifier = "entity.vms.OnboardingEmpPrivateInfo";

    @Override
    public String getEntityIdentifier() {
        return identifier;
    }

}