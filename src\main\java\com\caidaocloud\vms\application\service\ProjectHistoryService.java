package com.caidaocloud.vms.application.service;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.vms.application.dto.ProjectHistoryQueryDTO;
import com.caidaocloud.vms.application.vo.ProjectHistoryDetailVO;
import com.caidaocloud.vms.application.vo.ProjectHistoryPageVO;
import com.caidaocloud.vms.domain.project.entity.ProjectHistory;
import com.caidaocloud.vms.domain.project.entity.ProjectHistoryDetail;
import com.caidaocloud.vms.domain.project.enums.HistoryType;
import com.caidaocloud.vms.domain.project.repository.ProjectHistoryDetailRepository;
import com.caidaocloud.vms.domain.project.repository.ProjectHistoryRepository;
import com.googlecode.totallylazy.Sequences;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * 项目历史服务类
 *
 * <AUTHOR>
 * @date 2025/6/5
 */
@Service
public class ProjectHistoryService {

	@Autowired
	private ProjectHistoryRepository projectHistoryRepository;
	@Autowired
	private ProjectHistoryDetailRepository projectHistoryDetailRepository;


	public void saveDetail(ProjectHistoryDetail detail) {
		projectHistoryDetailRepository.saveOrUpdate(detail);
	}

	public void saveHistory(ProjectHistory history) {
		projectHistoryRepository.saveOrUpdate(history);
	}

	public void rollbackHistory(ProjectHistory projectHistory) {
		projectHistoryRepository.rollbackHistory(projectHistory);
	}

	public Optional<ProjectHistory> loadByBusinessKey(String businessKey) {
		String bid = StringUtils.substringAfterLast(businessKey, "_");
		return projectHistoryRepository.getById(bid);
	}

	public List<ProjectHistoryDetail> loadDetail(String historyId) {
		return projectHistoryDetailRepository.getByHistoryId(historyId);
	}

	/**
	 * 分页查询审批通过的历史记录
	 *
	 * @param queryDTO 查询条件
	 * @return 分页结果
	 */
	public PageResult<ProjectHistoryPageVO> findApprovedByPage(ProjectHistoryQueryDTO queryDTO) {
		PageResult<ProjectHistoryDetail> pageResult = projectHistoryDetailRepository.findApprovedByPage(queryDTO);

		// 转换为VO
		List<ProjectHistoryPageVO> voList = Sequences.sequence(pageResult.getItems())
				.map(this::convertToPageVO)
				.toList();

		PageResult<ProjectHistoryPageVO> result = new PageResult<>();
		result.setItems(voList);
		result.setTotal(pageResult.getTotal());
		result.setPageNo(pageResult.getPageNo());
		result.setPageSize(pageResult.getPageSize());

		return result;
	}

	/**
	 * 获取历史记录详情
	 *
	 * @param detailId 历史记录ID
	 * @return 历史记录详情
	 */
	public ProjectHistoryDetailVO getHistoryDetail(String detailId) {
		Optional<ProjectHistoryDetail> historyOpt = projectHistoryDetailRepository.getById(detailId);
		if (!historyOpt.isPresent()) {
			throw new ServerException("数据不存在");
		}

		return ObjectConverter.convert(historyOpt.get(), ProjectHistoryDetailVO.class);
	}

	/**
	 * 转换为分页VO
	 */
	private ProjectHistoryPageVO convertToPageVO(ProjectHistoryDetail history) {
		ProjectHistoryPageVO vo = ObjectConverter.convert(history, ProjectHistoryPageVO.class);
		vo.setHistoryType(history.getType());
		vo.setSubmitTime(history.getCreateTime());
		// 生成变更缩略信息
		vo.setChangeSummary(history.getChangeSummary());

		// TODO: 设置提交人和审批人信息（需要调用员工服务）

		return vo;
	}

}
