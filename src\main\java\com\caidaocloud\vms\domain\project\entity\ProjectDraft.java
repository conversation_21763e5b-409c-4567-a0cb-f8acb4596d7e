package com.caidaocloud.vms.domain.project.entity;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.vms.domain.base.entity.BaseEntity;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Data
@Slf4j
public class ProjectDraft extends BaseEntity {

    private String targetId;
    private String projectId;
    private String positionId;

    private EnumSimple type;
    private EnumSimple subType;
    private EnumSimple operation;

    // TODO: 2025/10/24 第一次创建时，记录origin ，后续根据origin和snapshot生成change
    private String origin;
    private String snapshot;

    public static String identifier = "entity.vms.ProjectDraft";

    @Override
    public String getEntityIdentifier() {
        return identifier;
    }
}