package com.caidaocloud.vms.application.vo;

import java.math.BigDecimal;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.vms.domain.project.enums.QuotationMode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 岗位下拉选择VO
 * 
 * <AUTHOR>
 * @date 2025/10/30
 */
@Data
@ApiModel(description = "岗位下拉选择信息")
public class PositionSelectVO {
    
    @ApiModelProperty(value = "岗位ID")
    private String positionId;

    @ApiModelProperty(value = "岗位名称")
    private String positionName;
    
    @ApiModelProperty(value = "岗位编码")
    private String positionCode;


    /**
     * 报价模式：按小时、按天、按月、按项目、固定价格
     */
    private QuotationMode quotationMode;

    /**
     * 报价金额
     */
    private BigDecimal quotationValue;

}
