package com.caidaocloud.vms.application.service;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.vms.application.dto.OnboardingSaveDTO;
import com.caidaocloud.vms.domain.employee.entity.OnboardingEmployee;
import com.caidaocloud.vms.domain.project.enums.QuotationMode;
import com.caidaocloud.vms.domain.base.enums.ApprovalStatus;
import com.caidaocloud.vms.domain.employee.entity.Employee;
import com.caidaocloud.vms.domain.employee.entity.EmployeeChangeRecord;
import com.caidaocloud.vms.domain.employee.entity.onboarding.*;
import com.caidaocloud.vms.domain.employee.enums.EmployeeChangeType;
import com.caidaocloud.vms.domain.employee.repository.EmployeeChangeRecordRepository;
import com.caidaocloud.vms.domain.employee.repository.EmployeeRepository;
import com.caidaocloud.vms.domain.employee.repository.OnboardingRepository;
import com.caidaocloud.vms.domain.project.enums.WorkflowConfig;
import com.caidaocloud.vms.domain.base.service.WorkflowService;
import com.caidaocloud.workflow.enums.WfCallbackTriggerOperationEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

import java.math.BigDecimal;

/**
 * 预入职Service
 * 
 * <AUTHOR> Zhou
 * @date 2025/10/30
 */
@Service
@Slf4j
public class OnboardingService {

    @Autowired
    private EmployeeRepository employeeRepository;

    @Autowired
    private EmployeeChangeRecordRepository employeeChangeRecordRepository;

    @Autowired
    private OnboardingRepository onboardingRepository;

    @Autowired
    private WorkflowService workflowService;

    /**
     * 初始化预入职工作流
     * 在服务启动时自动注册预入职工作流
     */
    @PostConstruct
    public void initOnboardingWorkflow() {
        try {
            // 注册预入职工作流，会自动注册function和callback
            workflowService.register(WorkflowConfig.EMPLOYEE_ONBOARDING);
            log.info("预入职工作流初始化成功");
        } catch (Exception e) {
            log.error("预入职工作流初始化失败", e);
        }
    }

    /**
     * 保存预入职信息
     * 
     * @param onboardingDTO 预入职信息
     * @return 员工ID
     */
    public String saveOnboarding(OnboardingSaveDTO onboardingDTO) {
        log.info("开始保存预入职信息: {}", onboardingDTO.getWorkInfo().getName());

        // 1. 校验工号是否已存在
        String workno = onboardingDTO.getWorkInfo() != null ? onboardingDTO.getWorkInfo().getWorkno() : null;
        if (workno == null) {
            throw new ServerException("员工工号不能为空");
        }
        Employee existingEmployee = employeeRepository.getByWorkno(workno);
        if (existingEmployee != null) {
            throw new ServerException("工号已存在: " + workno);
        }
        // 保存预入职实体数据
        String empId = saveOnboardingData(onboardingDTO);

        // 2. 创建员工实体
        Employee employee = createEmployeeFromDTO(onboardingDTO, empId);

        // 3. 保存员工信息
        employeeRepository.saveOrUpdate(employee);
        log.info("员工信息保存成功，员工ID: {}", empId);

        // 5. 创建入离项记录
        EmployeeChangeRecord changeRecord = createChangeRecord(employee, onboardingDTO);
        employeeChangeRecordRepository.saveOrUpdate(changeRecord);
        log.info("入离项记录创建成功，记录ID: {}", changeRecord.getBid());

        // 6. 发起工作流
        try {
            startOnboardingWorkflow(changeRecord);
            log.info("预入职工作流发起成功，单据ID: {}", changeRecord.getBid());
        } catch (Exception e) {
            log.error("预入职工作流发起失败，开始回滚数据，员工ID: {}, 单据ID: {}", empId, changeRecord.getBid(), e);
            // 工作流发起失败，回滚所有已创建的数据
            rollbackOnboardingData(empId, changeRecord.getBid());
            throw new ServerException("预入职工作流发起失败", e);
        }

        return empId;
    }

    /**
     * 保存预入职相关实体数据
     *
     * @param onboardingDTO 预入职DTO
     * @return
     */
    private String saveOnboardingData(OnboardingSaveDTO onboardingDTO) {
        log.info("开始保存预入职数据");
        OnboardingEmpWorkInfo workInfo = ObjectConverter.convert(onboardingDTO.getWorkInfo(),
                OnboardingEmpWorkInfo.class);
        OnboardingEmpPrivateInfo privateInfo = ObjectConverter.convert(onboardingDTO.getPrivateInfo(),
                OnboardingEmpPrivateInfo.class);
        OnboardingContract contract = ObjectConverter.convert(onboardingDTO.getContractInfo(),
                OnboardingContract.class);
        OnboardingOtherContract otherContract = ObjectConverter.convert(onboardingDTO.getOtherContractInfo(),
                OnboardingOtherContract.class);
        OnboardingSalary salary = ObjectConverter.convert(onboardingDTO.getSalaryInfo(), OnboardingSalary.class);
        OnboardingEmployee onboardingEmployee = new OnboardingEmployee(workInfo, privateInfo, contract, otherContract,
                salary);
        String empId = onboardingRepository.saveOrUpdate(onboardingEmployee);

        log.info("预入职实体数据保存完成，员工ID: {}", empId);
        return empId;
    }

    /**
     * 从DTO创建员工实体
     * 
     * @param dto   预入职DTO
     * @param empId
     * @return 员工实体
     */
    private Employee createEmployeeFromDTO(OnboardingSaveDTO dto, String empId) {
        Employee employee = new Employee();
        employee.setBid(empId);
        // 工作信息 - 只设置Employee实体中存在的字段
        if (dto.getWorkInfo() != null) {
            OnboardingSaveDTO.OnboardingWorkInfoDTO workInfo = dto.getWorkInfo();
            employee.setWorkno(workInfo.getWorkno());
            employee.setName(workInfo.getName());
            employee.setHireDate(workInfo.getHireDate());
            employee.setCompanyId(workInfo.getCompany());
            // 设置员工状态为null，待后续根据业务需要设置具体状态
            employee.setEmpStatus(null);
        }

        // 合同信息
        if (dto.getContractInfo() != null) {
            OnboardingSaveDTO.OnboardingContractDTO contractInfo = dto.getContractInfo();
            employee.setContractStartDate(contractInfo.getStartDate());
            employee.setContractEndDate(contractInfo.getEndDate());
        }

        // 薪资信息
        if (dto.getSalaryInfo() != null) {
            OnboardingSaveDTO.OnboardingSalaryDTO salaryInfo = dto.getSalaryInfo();
            employee.setSalary(salaryInfo.getSalary());
            employee.setQuotationMode(QuotationMode.fromValue(salaryInfo.getQuotationMode()));
            employee.setQuotationValue(salaryInfo.getQuotationValue());
        }

        // VMS相关信息
        employee.setSupplierId(dto.getSupplierId());
        employee.setSupplierTxt(dto.getSupplierTxt());
        employee.setProjectId(dto.getProjectId());
        employee.setPositionId(dto.getPositionId());

        return employee;
    }

    /**
     * 创建入离项记录
     * 
     * @param employee 员工实体
     * @param dto      预入职DTO
     * @return 入离项记录
     */
    private EmployeeChangeRecord createChangeRecord(Employee employee, OnboardingSaveDTO dto) {
        EmployeeChangeRecord record = new EmployeeChangeRecord();

        // 设置交接类型为预入职
        record.setType(EmployeeChangeType.PRE_ONBOARDING);

        // 设置基本信息
        record.setProjectId(dto.getProjectId());
        record.setSupplierId(dto.getSupplierId());
        record.setPositionId(dto.getPositionId());
        record.setEmpId(employee.getBid());

        // 从嵌套DTO获取信息
        if (dto.getWorkInfo() != null) {
            record.setCompanyId(dto.getWorkInfo().getCompany());
            record.setEmpName(dto.getWorkInfo().getName());
        }

        record.setStartTime(dto.getWorkInfo().getHireDate());
        record.setEndTime(dto.getWorkInfo().getLeaveDate());

        // 设置薪资信息
        if (dto.getSalaryInfo() != null) {
            OnboardingSaveDTO.OnboardingSalaryDTO salaryInfo = dto.getSalaryInfo();
            if (salaryInfo.getSalary() != null) {
                record.setSalary(new BigDecimal(salaryInfo.getSalary()));
            }
            record.setQuotationMode(QuotationMode.fromValue(salaryInfo.getQuotationMode()));
            record.setQuotationValue(dto.getSalaryInfo().getQuotationValue());
        }

        // 设置审批状态为待审批
        record.setApprovalStatus(ApprovalStatus.PENDING);

        // 设置业务ID为员工ID
        record.setBusinessId(employee.getBid());

        return record;
    }

    /**
     * 发起预入职工作流
     *
     * @param changeRecord 员工变更记录
     */
    private void startOnboardingWorkflow(EmployeeChangeRecord changeRecord) {
        log.info("开始发起预入职工作流，单据ID: {}", changeRecord.getBid());
        try {
            workflowService.startWorkflow(changeRecord, WorkflowConfig.EMPLOYEE_ONBOARDING.getCode());
            log.info("预入职工作流发起成功，单据ID: {}", changeRecord.getBid());
        } catch (Exception e) {
            log.error("预入职工作流发起失败，单据ID: {}", changeRecord.getBid(), e);
            throw e;
        }
    }

    /**
     * 回滚预入职数据
     * 当工作流发起失败时，删除已创建的员工、变更记录和预入职数据
     *
     * @param empId          员工ID
     * @param changeRecordId 变更记录ID
     */
    private void rollbackOnboardingData(String empId, String changeRecordId) {
        log.info("开始回滚预入职数据，员工ID: {}, 变更记录ID: {}", empId, changeRecordId);

        try {
            // 1. 删除员工变更记录
            if (changeRecordId != null) {
                employeeChangeRecordRepository.deleteById(changeRecordId);
                log.info("删除员工变更记录成功，ID: {}", changeRecordId);
            }

            // 2. 删除预入职相关数据
            if (empId != null) {
                onboardingRepository.deleteAllOnboardingDataByEmpId(empId);
                log.info("删除预入职数据成功，员工ID: {}", empId);
            }

            // 3. 删除员工信息
            if (empId != null) {
                employeeRepository.deleteById(empId);
                log.info("删除员工信息成功，员工ID: {}", empId);
            }

            log.info("预入职数据回滚完成，员工ID: {}", empId);
        } catch (Exception e) {
            log.error("预入职数据回滚失败，员工ID: {}, 变更记录ID: {}", empId, changeRecordId, e);
            // 回滚失败不抛出异常，避免掩盖原始异常
        }
    }

    public void callback(String businessKey, String tenantId, WfCallbackTriggerOperationEnum callback) {
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setTenantId(tenantId);
        // 回调默认用户id为 0
        userInfo.setUserId(0L);
        SecurityUserUtil.setSecurityUserInfo(userInfo);
        try {
            doCallback(businessKey, callback);
        } catch (Exception e) {
            throw new ServerException("callback occurs error", e);
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
        }
    }

    private void doCallback(String businessKey, WfCallbackTriggerOperationEnum callback) {
        String businessId = StringUtils.substringAfterLast(businessKey, "_");
        EmployeeChangeRecord record = employeeChangeRecordRepository.getById(businessId);
        switch (callback) {
        case APPROVED:
            record.approved();
            employeeChangeRecordRepository.saveOrUpdate(record);
            // TODO: 2025/11/1  Onboarding feign  20251208暂停
            break;
        case REFUSED:
            record.rejected();
            employeeChangeRecordRepository.saveOrUpdate(record);
            break;
        default:
            throw new ServerException("not support");
        }
    }
}
