package com.caidaocloud.vms.application.vo;

import com.caidaocloud.hrpaas.metadata.sdk.annotation.DisplayAsArray;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.vms.domain.project.entity.ProjectChange;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(description = "项目历史详情 VO")
public class ProjectHistoryDetailVO {

    @ApiModelProperty(value = "历史记录ID")
    private String bid;

    @ApiModelProperty(value = "项目ID")
    private String projectId;

    @ApiModelProperty(value = "提交人")
    private EmpSimple submitter;

    @ApiModelProperty(value = "审批人")
    private EmpSimple approver;

    @ApiModelProperty(value = "提交时间")
    private Long submitTime;

    @ApiModelProperty(value = "审批时间")
    private Long approveTime;

    @ApiModelProperty(value = "审批状态")
    private EnumSimple approveStatus;

    private String snapshot;

    @DisplayAsArray
    private List<ProjectChange> change;

}
