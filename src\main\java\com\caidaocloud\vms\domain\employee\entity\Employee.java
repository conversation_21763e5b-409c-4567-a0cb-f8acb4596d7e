package com.caidaocloud.vms.domain.employee.entity;

import com.caidaocloud.hr.service.enums.system.EmpStatusEnum;
import com.caidaocloud.vms.domain.base.entity.BaseEntity;
import com.caidaocloud.vms.domain.project.enums.QuotationMode;
import lombok.Data;

@Data
public class Employee extends BaseEntity {

    private String workno;

    private String name;

    private String supplierId;

    private String supplierTxt;

    private String projectId;

    private String positionId;

    private String companyId;

    private Long hireDate;

    private Long contractStartDate;

    private Long contractEndDate;

    private String salary;

    private QuotationMode quotationMode;

    private String quotationValue;

    private String managementFee;

    private EmpStatusEnum empStatus;

    public static String identifier = "entity.vms.Employee";

    @Override
    public String getEntityIdentifier() {
        return identifier;
    }
}