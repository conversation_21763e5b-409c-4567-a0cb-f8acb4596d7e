<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.caidaocloud</groupId>
        <artifactId>caidaocloud-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <groupId>com.caidaocloud</groupId>
    <artifactId>caidaocloud-vms-service</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>jar</packaging>

    <name>caidaocloud-vms-service</name>
    <description>才到云供应商管理服务</description>

    <properties>
        <java.version>8</java.version>
        <kotlin.version>${kotlin.stdlib.version}</kotlin.version>
    </properties>

    <dependencies>
        <!-- 添加您需要的依赖 -->
        <dependency>
            <groupId>com.googlecode.totallylazy</groupId>
            <artifactId>totallylazy</artifactId>
        </dependency>
        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>galaxy-service-security</artifactId>
        </dependency>
        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>workflow-sdk</artifactId>
            <version>2.0.6-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>metadata-sdk</artifactId>
            <version>2.0.2-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-data-mongodb</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mongodb.morphia</groupId>
                    <artifactId>morphia</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>hr-core</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>galaxy-service-condition</artifactId>
                    <groupId>com.caidaocloud</groupId>
                </exclusion>
            </exclusions>
            <!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId></groupId>-->
<!--                    <artifactId></artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>