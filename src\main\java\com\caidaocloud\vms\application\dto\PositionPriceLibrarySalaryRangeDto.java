package com.caidaocloud.vms.application.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 岗位价格库薪资范围DTO
 *
 * <AUTHOR>
 * @date 2025/12/10
 */
@Data
@ApiModel(description = "岗位价格库薪资范围")
public class PositionPriceLibrarySalaryRangeDto {

    @ApiModelProperty(value = "岗位ID", required = true)
    private String postId;

    @ApiModelProperty(value = "组织ID", required = true)
    private String orgId;

    @ApiModelProperty(value = "最低薪资")
    private Integer minSalary;

    @ApiModelProperty(value = "最高薪资")
    private Integer maxSalary;

    @ApiModelProperty(value = "是否找到价格库记录")
    private Boolean found;

    @ApiModelProperty(value = "岗位名称")
    private String postName;

    @ApiModelProperty(value = "组织名称")
    private String orgName;

    /**
     * 创建未找到记录的响应
     */
    public static PositionPriceLibrarySalaryRangeDto notFound(String postId, String orgId) {
        PositionPriceLibrarySalaryRangeDto dto = new PositionPriceLibrarySalaryRangeDto();
        dto.setPostId(postId);
        dto.setOrgId(orgId);
        dto.setFound(false);
        return dto;
    }

    /**
     * 创建找到记录的响应
     */
    public static PositionPriceLibrarySalaryRangeDto found(String postId, String orgId, 
                                                          Integer minSalary, Integer maxSalary) {
        PositionPriceLibrarySalaryRangeDto dto = new PositionPriceLibrarySalaryRangeDto();
        dto.setPostId(postId);
        dto.setOrgId(orgId);
        dto.setMinSalary(minSalary);
        dto.setMaxSalary(maxSalary);
        dto.setFound(true);
        return dto;
    }
}
