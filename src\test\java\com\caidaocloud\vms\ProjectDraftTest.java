// package com.caidaocloud.vms;
//
// import com.caidaocloud.vms.domain.project.entity.ProjectDraft;
// import com.caidaocloud.vms.domain.project.entity.ProjectPosition;
// import com.caidaocloud.vms.domain.project.entity.ProjectHistoryDetail;
// import com.caidaocloud.vms.domain.project.enums.HistoryType;
// import com.caidaocloud.vms.domain.project.enums.OperationType;
// import com.caidaocloud.vms.domain.project.factory.ProjectHistoryFactory;
// import com.caidaocloud.vms.domain.project.repository.ProjectDraftRepository;
// import com.fasterxml.jackson.databind.ObjectMapper;
// import org.junit.jupiter.api.Test;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.boot.test.context.SpringBootTest;
// import org.springframework.test.context.ActiveProfiles;
//
// import java.util.List;
// import java.util.Optional;
//
// /**
//  * ProjectDraft功能测试
//  *
//  * <AUTHOR>
//  * @date 2025/10/10
//  */
// @SpringBootTest
// @ActiveProfiles("test")
// public class ProjectDraftTest {
//
//     @Autowired
//     private ProjectDraftRepository projectDraftRepository;
//
//     @Autowired
//     private ProjectHistoryFactory projectHistoryFactory;
//
//     private final ObjectMapper objectMapper = new ObjectMapper();
//
//     /**
//      * 测试ProjectDraft基本CRUD操作
//      */
//     @Test
//     public void testProjectDraftCrud() {
//         // 准备测试数据
//         String projectId = "test_project_draft_1";
//         String targetId = "test_target_1";
//
//         // 创建草稿
//         ProjectDraft draft = new ProjectDraft();
//         draft.setTargetId(targetId);
//         draft.setProjectId(projectId);
//         draft.setType(HistoryType.POSITION.toEnumSimple());
//         draft.setOperation(OperationType.CREATE.toEnumSimple());
//         draft.setSnapshot("{\"test\": \"data\"}");
//
//         // 保存草稿
//         String draftId = projectDraftRepository.saveOrUpdate(draft);
//         assert draftId != null : "草稿ID不应为空";
//
//         // 根据ID查询草稿
//         Optional<ProjectDraft> savedDraft = projectDraftRepository.getById(draftId);
//         assert savedDraft.isPresent() : "应该能查询到保存的草稿";
//         assert targetId.equals(savedDraft.get().getTargetId()) : "targetId应该匹配";
//         assert projectId.equals(savedDraft.get().getProjectId()) : "projectId应该匹配";
//
//         // 根据targetId查询草稿
//         Optional<ProjectDraft> draftByTargetId = projectDraftRepository.getByTargetId(targetId);
//         assert draftByTargetId.isPresent() : "应该能根据targetId查询到草稿";
//         assert draftId.equals(draftByTargetId.get().getBid()) : "草稿ID应该匹配";
//
//         // 根据项目ID和类型查询草稿列表
//         List<ProjectDraft> drafts = projectDraftRepository.getByProjectIdAndType(projectId, HistoryType.POSITION);
//         assert !drafts.isEmpty() : "应该能查询到草稿列表";
//         assert drafts.size() >= 1 : "至少应该有一个草稿";
//
//         // 更新草稿
//         draft.setSnapshot("{\"updated\": \"data\"}");
//         String updatedDraftId = projectDraftRepository.saveOrUpdate(draft);
//         assert draftId.equals(updatedDraftId) : "更新后的草稿ID应该保持不变";
//
//         // 验证更新
//         Optional<ProjectDraft> updatedDraft = projectDraftRepository.getById(draftId);
//         assert updatedDraft.isPresent() : "更新后应该能查询到草稿";
//         assert "{\"updated\": \"data\"}".equals(updatedDraft.get().getSnapshot()) : "快照应该已更新";
//
//         // 删除草稿
//         projectDraftRepository.delete(draftId);
//         Optional<ProjectDraft> deletedDraft = projectDraftRepository.getById(draftId);
//         assert !deletedDraft.isPresent() : "删除后应该查询不到草稿";
//
//         System.out.println("✓ 测试通过：ProjectDraft基本CRUD操作");
//     }
//     //
//     // /**
//     //  * 测试ProjectHistoryFactory的createDraft方法
//     //  */
//     // @Test
//     // public void testCreateDraftWithFactory() {
//     //     // 准备测试数据
//     //     String projectId = "test_project_draft_2";
//     //
//     //     // 创建一个ProjectPosition实体
//     //     ProjectPosition position = new ProjectPosition();
//     //     position.setBid("test_position_1");
//     //     position.setProjectId(projectId);
//     //     position.setPosition("测试岗位");
//     //     position.setCompany("测试公司");
//     //     position.setOrganization("测试组织");
//     //
//     //     // 使用工厂方法创建草稿
//     //     projectHistoryFactory.createDraft(projectId, HistoryType.POSITION, OperationType.CREATE, position);
//     //
//     //     // 验证草稿已创建
//     //     Optional<ProjectDraft> draft = projectDraftRepository.getByTargetId("test_position_1");
//     //     assert draft.isPresent() : "应该能查询到创建的草稿";
//     //     assert projectId.equals(draft.get().getProjectId()) : "项目ID应该匹配";
//     //     assert HistoryType.POSITION.getValue().equals(draft.get().getType().getValue()) : "类型应该匹配";
//     //     assert OperationType.CREATE.getValue().equals(draft.get().getOperation().getValue()) : "操作类型应该匹配";
//     //
//     //     // 验证快照内容
//     //     String snapshot = draft.get().getSnapshot();
//     //     assert snapshot != null && !snapshot.isEmpty() : "快照不应为空";
//     //
//     //     try {
//     //         // 验证快照是有效的JSON
//     //         ProjectPosition deserializedPosition = objectMapper.readValue(snapshot, ProjectPosition.class);
//     //         assert "测试岗位".equals(deserializedPosition.getPosition()) : "反序列化的岗位名称应该匹配";
//     //     } catch (Exception e) {
//     //         assert false : "快照应该是有效的JSON: " + e.getMessage();
//     //     }
//     //
//     //     // 测试更新草稿
//     //     position.setPosition("更新后的岗位");
//     //     projectHistoryFactory.createDraft(projectId, HistoryType.POSITION, OperationType.UPDATE, position);
//     //
//     //     // 验证草稿已更新
//     //     Optional<ProjectDraft> updatedDraft = projectDraftRepository.getByTargetId("test_position_1");
//     //     assert updatedDraft.isPresent() : "应该能查询到更新的草稿";
//     //     assert OperationType.UPDATE.getValue().equals(updatedDraft.get().getOperation().getValue()) : "操作类型应该已更新";
//     //
//     //     // 清理测试数据
//     //     projectDraftRepository.deleteByTargetId("test_position_1");
//     //
//     //     System.out.println("✓ 测试通过：ProjectHistoryFactory的createDraft方法");
//     // }
//     //
//     // /**
//     //  * 测试新增实体时的临时ID生成
//     //  */
//     // @Test
//     // public void testCreateDraftWithTempId() {
//     //     // 准备测试数据
//     //     String projectId = "test_project_draft_3";
//     //
//     //     // 创建一个没有bid的ProjectPosition实体（模拟新增场景）
//     //     ProjectPosition position = new ProjectPosition();
//     //     position.setProjectId(projectId);
//     //     position.setPosition("新增岗位");
//     //     position.setCompany("新增公司");
//     //
//     //     // 使用工厂方法创建草稿
//     //     projectHistoryFactory.createDraft(projectId, HistoryType.POSITION, OperationType.CREATE, position);
//     //
//     //     // 验证实体已被分配临时ID
//     //     String tempId = position.getBid();
//     //     assert tempId != null : "实体应该被分配临时ID";
//     //     assert tempId.startsWith("temp_") : "临时ID应该以temp_开头";
//     //
//     //     // 验证草稿已创建
//     //     Optional<ProjectDraft> draft = projectDraftRepository.getByTargetId(tempId);
//     //     assert draft.isPresent() : "应该能查询到创建的草稿";
//     //     assert projectId.equals(draft.get().getProjectId()) : "项目ID应该匹配";
//     //
//     //     // 清理测试数据
//     //     projectDraftRepository.deleteByTargetId(tempId);
//     //
//     //     System.out.println("✓ 测试通过：新增实体时的临时ID生成");
//     // }
//
//     /**
//      * 测试根据项目ID删除所有草稿
//      */
//     @Test
//     public void testDeleteByProjectId() {
//         // 准备测试数据
//         String projectId = "test_project_draft_4";
//
//         // 创建多个草稿
//         for (int i = 1; i <= 3; i++) {
//             ProjectDraft draft = new ProjectDraft();
//             draft.setTargetId("target_" + i);
//             draft.setProjectId(projectId);
//             draft.setType(HistoryType.POSITION.toEnumSimple());
//             draft.setOperation(OperationType.CREATE.toEnumSimple());
//             draft.setSnapshot("{\"test\": " + i + "}");
//             projectDraftRepository.saveOrUpdate(draft);
//         }
//
//         // 验证草稿已创建
//         List<ProjectDraft> drafts = projectDraftRepository.getByProjectIdAndType(projectId, HistoryType.POSITION);
//         assert drafts.size() == 3 : "应该有3个草稿";
//
//         // 删除项目的所有草稿
//         projectDraftRepository.deleteByProjectId(projectId);
//
//         // 验证草稿已删除
//         List<ProjectDraft> remainingDrafts = projectDraftRepository.getByProjectIdAndType(projectId,
//                 HistoryType.POSITION);
//         assert remainingDrafts.isEmpty() : "所有草稿应该已被删除";
//
//         System.out.println("✓ 测试通过：根据项目ID删除所有草稿");
//     }
//
//     /**
//      * 测试根据草稿生成历史详情记录
//      */
//     @Test
//     public void testGenerateHistoryDetailFromDraft() {
//         // 准备测试数据
//         String projectId = "test_project_draft_5";
//         String historyId = "test_history_1";
//
//         // 创建一个ProjectPosition实体作为原始数据
//         ProjectPosition originalPosition = new ProjectPosition();
//         originalPosition.setBid("position_123");
//         originalPosition.setProjectId(projectId);
//         originalPosition.setPosition("原始岗位");
//         originalPosition.setCompany("原始公司");
//
//         // 创建一个修改后的ProjectPosition实体
//         ProjectPosition updatedPosition = new ProjectPosition();
//         updatedPosition.setBid("position_123");
//         updatedPosition.setProjectId(projectId);
//         updatedPosition.setPosition("修改后的岗位");
//         updatedPosition.setCompany("修改后的公司");
//
//         // 创建草稿
//         ProjectDraft draft = new ProjectDraft();
//         draft.setTargetId("position_123");
//         draft.setProjectId(projectId);
//         draft.setType(HistoryType.POSITION.toEnumSimple());
//         draft.setOperation(OperationType.UPDATE.toEnumSimple());
//
//         try {
//             String snapshot = objectMapper.writeValueAsString(updatedPosition);
//             draft.setSnapshot(snapshot);
//         } catch (Exception e) {
//             assert false : "序列化实体失败: " + e.getMessage();
//         }
//
//         // 使用工厂方法生成历史详情记录
//         ProjectHistoryDetail detail = projectHistoryFactory.generateHistoryDetailFromDraft(
//                 originalPosition, draft, historyId);
//
//         // 验证历史详情记录
//         assert detail != null : "历史详情记录不应为空";
//         assert historyId.equals(detail.getHistoryId()) : "历史ID应该匹配";
//         assert HistoryType.POSITION.getValue().equals(detail.getType().getValue()) : "历史类型应该匹配";
//         assert OperationType.UPDATE.getValue().equals(detail.getOperation().getValue()) : "操作类型应该匹配";
//
//         // 验证快照和结果
//         assert detail.getSnapshot() != null : "快照不应为空";
//         assert detail.getResult() != null : "结果不应为空";
//         assert detail.getResult().equals(draft.getSnapshot()) : "结果应该与草稿快照匹配";
//
//         // 验证变更记录
//         assert detail.getChange() != null : "变更记录不应为空";
//         // 注意：这里可能需要根据实际的generateChanges实现来验证
//
//         System.out.println("✓ 测试通过：根据草稿生成历史详情记录");
//     }
//
//     /**
//      * 测试批量根据草稿生成历史详情记录
//      */
//     @Test
//     public void testGenerateHistoryDetailsFromDrafts() {
//         // 准备测试数据
//         String projectId = "test_project_draft_6";
//         String historyId = "test_history_2";
//
//         // 创建多个草稿
//         for (int i = 1; i <= 3; i++) {
//             ProjectPosition position = new ProjectPosition();
//             position.setBid("position_" + i);
//             position.setProjectId(projectId);
//             position.setPosition("岗位" + i);
//             position.setCompany("公司" + i);
//
//             ProjectDraft draft = new ProjectDraft();
//             draft.setTargetId("position_" + i);
//             draft.setProjectId(projectId);
//             draft.setType(HistoryType.POSITION.toEnumSimple());
//             draft.setOperation(OperationType.CREATE.toEnumSimple());
//
//             try {
//                 String snapshot = objectMapper.writeValueAsString(position);
//                 draft.setSnapshot(snapshot);
//             } catch (Exception e) {
//                 assert false : "序列化实体失败: " + e.getMessage();
//             }
//
//             projectDraftRepository.saveOrUpdate(draft);
//         }
//
//         // 批量生成历史详情记录
//         List<ProjectHistoryDetail> details = projectHistoryFactory.generateHistoryDetailsFromDrafts(
//                 projectId, HistoryType.POSITION, historyId);
//
//         // 验证结果
//         assert details != null : "历史详情记录列表不应为空";
//         assert details.size() == 3 : "应该生成3个历史详情记录";
//
//         for (ProjectHistoryDetail detail : details) {
//             assert historyId.equals(detail.getHistoryId()) : "历史ID应该匹配";
//             assert HistoryType.POSITION.getValue().equals(detail.getType().getValue()) : "历史类型应该匹配";
//             assert OperationType.CREATE.getValue().equals(detail.getOperation().getValue()) : "操作类型应该匹配";
//             assert detail.getSnapshot() != null : "快照不应为空";
//             assert detail.getResult() != null : "结果不应为空";
//         }
//
//         // 清理测试数据
//         projectDraftRepository.deleteByProjectId(projectId);
//
//         System.out.println("✓ 测试通过：批量根据草稿生成历史详情记录");
//     }
// }
