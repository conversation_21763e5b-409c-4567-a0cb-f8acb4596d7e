package com.caidaocloud.vms.domain.employee.repository;

import com.caidaocloud.vms.domain.employee.entity.OnboardingEmployee;
import com.caidaocloud.vms.domain.employee.entity.onboarding.*;

import java.util.List;

/**
 * 预入职Repository接口
 *
 * <AUTHOR>
 * @date 2025/10/30
 */
public interface OnboardingRepository {

    String saveOrUpdate(OnboardingEmployee onboardingEmployee);

    OnboardingEmployee detail(String empId);

    /**
     * 保存或更新员工工作信息
     *
     * @param workInfo 员工工作信息
     * @return 记录ID
     */
    String saveOrUpdateWorkInfo(OnboardingEmpWorkInfo workInfo);

    /**
     * 保存或更新员工个人信息
     *
     * @param privateInfo 员工个人信息
     * @return 记录ID
     */
    String saveOrUpdatePrivateInfo(OnboardingEmpPrivateInfo privateInfo);

    /**
     * 保存或更新合同信息
     *
     * @param contract 合同信息
     * @return 记录ID
     */
    String saveOrUpdateContract(OnboardingContract contract);

    /**
     * 保存或更新薪资信息
     *
     * @param salary 薪资信息
     * @return 记录ID
     */
    String saveOrUpdateSalary(OnboardingSalary salary);

    /**
     * 保存或更新其他合同信息
     *
     * @param otherContract 其他合同信息
     * @return 记录ID
     */
    String saveOrUpdateOtherContract(OnboardingOtherContract otherContract);

    /**
     * 保存或更新教育经历
     *
     * @param eduExp 教育经历
     * @return 记录ID
     */
    String saveOrUpdateEduExp(OnboardingEmpEduExp eduExp);

    /**
     * 批量保存教育经历
     *
     * @param eduExpList 教育经历列表
     * @return 记录ID列表
     */
    List<String> batchSaveEduExp(List<OnboardingEmpEduExp> eduExpList);

    /**
     * 根据员工ID删除所有预入职数据
     *
     * @param empId 员工ID
     */
    void deleteAllOnboardingDataByEmpId(String empId);
    //
    // /**
    // * 保存或更新员工工作信息
    // *
    // * @param workInfo 员工工作信息
    // * @return 记录ID
    // */
    // String saveOrUpdateWorkInfo(OnboardingEmpWorkInfo workInfo);
    //
    // /**
    // * 保存或更新员工个人信息
    // *
    // * @param privateInfo 员工个人信息
    // * @return 记录ID
    // */
    // String saveOrUpdatePrivateInfo(OnboardingEmpPrivateInfo privateInfo);
    //
    // /**
    // * 保存或更新合同信息
    // *
    // * @param contract 合同信息
    // * @return 记录ID
    // */
    // String saveOrUpdateContract(OnboardingContract contract);
    //
    // /**
    // * 保存或更新薪资信息
    // *
    // * @param salary 薪资信息
    // * @return 记录ID
    // */
    // String saveOrUpdateSalary(OnboardingSalary salary);
    //
    // /**
    // * 保存或更新其他合同信息
    // *
    // * @param otherContract 其他合同信息
    // * @return 记录ID
    // */
    // String saveOrUpdateOtherContract(OnboardingOtherContract otherContract);
    //
    // /**
    // * 保存或更新教育经历
    // *
    // * @param eduExp 教育经历
    // * @return 记录ID
    // */
    // String saveOrUpdateEduExp(OnboardingEmpEduExp eduExp);
    //
    // /**
    // * 批量保存教育经历
    // *
    // * @param eduExpList 教育经历列表
    // * @return 记录ID列表
    // */
    // List<String> batchSaveEduExp(List<OnboardingEmpEduExp> eduExpList);
    //
    // /**
    // * 根据员工ID获取工作信息
    // *
    // * @param empId 员工ID
    // * @return 工作信息
    // */
    // OnboardingEmpWorkInfo getWorkInfoByEmpId(String empId);
    //
    // /**
    // * 根据员工ID获取个人信息
    // *
    // * @param empId 员工ID
    // * @return 个人信息
    // */
    // OnboardingEmpPrivateInfo getPrivateInfoByEmpId(String empId);
    //
    // /**
    // * 根据员工ID获取合同信息
    // *
    // * @param empId 员工ID
    // * @return 合同信息
    // */
    // OnboardingContract getContractByEmpId(String empId);
    //
    // /**
    // * 根据员工ID获取薪资信息
    // *
    // * @param empId 员工ID
    // * @return 薪资信息
    // */
    // OnboardingSalary getSalaryByEmpId(String empId);
    //
    // /**
    // * 根据员工ID获取其他合同信息
    // *
    // * @param empId 员工ID
    // * @return 其他合同信息
    // */
    // OnboardingOtherContract getOtherContractByEmpId(String empId);
    //
    // /**
    // * 根据员工ID获取教育经历列表
    // *
    // * @param empId 员工ID
    // * @return 教育经历列表
    // */
    // List<OnboardingEmpEduExp> getEduExpListByEmpId(String empId);
}
