package com.caidaocloud.vms.application.vo;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(description = "项目历史分页信息 VO")
public class ProjectHistoryPageVO {

    @ApiModelProperty(value = "历史记录ID")
    private String bid;

    @ApiModelProperty(value = "项目ID")
    private String projectId;

    @ApiModelProperty(value = "变更类型")
    private EnumSimple historyType;

    @ApiModelProperty(value = "变更缩略信息")
    private String changeSummary;

    @ApiModelProperty(value = "提交人")
    private EmpSimple submitter;

    @ApiModelProperty(value = "审批人")
    private EmpSimple approver;

    @ApiModelProperty(value = "提交时间")
    private Long submitTime;

    @ApiModelProperty(value = "审批时间")
    private Long approveTime;

    @ApiModelProperty(value = "审批状态")
    private EnumSimple approveStatus;

}
