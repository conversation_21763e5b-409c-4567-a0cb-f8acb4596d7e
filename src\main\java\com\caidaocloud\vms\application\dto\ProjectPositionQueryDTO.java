package com.caidaocloud.vms.application.dto;

import com.caidaocloud.dto.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "项目查询条件")
public class ProjectPositionQueryDTO extends BasePage {
    @ApiModelProperty(value = "项目id")
    private String projectId;
    
    @ApiModelProperty(value = "岗位名称")
    private String positionName;
}