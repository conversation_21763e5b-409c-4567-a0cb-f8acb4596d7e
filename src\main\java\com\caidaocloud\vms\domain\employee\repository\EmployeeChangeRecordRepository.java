package com.caidaocloud.vms.domain.employee.repository;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.vms.application.dto.EmployeeChangeRecordQueryDTO;
import com.caidaocloud.vms.domain.employee.entity.EmployeeChangeRecord;

/**
 * 员工入离项记录Repository接口
 * 
 * <AUTHOR>
 * @date 2025/10/29
 */
public interface EmployeeChangeRecordRepository {

    /**
     * 保存或更新员工入离项记录
     * 
     * @param record 员工入离项记录
     */
    void saveOrUpdate(EmployeeChangeRecord record);

    /**
     * 根据ID获取员工入离项记录
     * 
     * @param recordId 记录ID
     * @return 员工入离项记录
     */
    EmployeeChangeRecord getById(String recordId);

    /**
     * 分页查询员工入离项记录
     * 
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    PageResult<EmployeeChangeRecord> findByPage(EmployeeChangeRecordQueryDTO queryDTO);

    /**
     * 删除员工入离项记录
     * 
     * @param recordId 记录ID
     */
    void deleteById(String recordId);
}
