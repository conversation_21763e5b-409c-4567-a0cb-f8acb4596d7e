// package com.caidaocloud.vms;
//
// import com.caidaocloud.dto.PageResult;
// import com.caidaocloud.vms.application.dto.PositionManagementQueryDTO;
// import com.caidaocloud.vms.application.service.PositionManagementService;
// import com.caidaocloud.vms.application.vo.PositionManagementPageVO;
// import org.junit.Test;
// import org.junit.jupiter.api.Test;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.boot.test.context.SpringBootTest;
//
// /**
//  * 岗位管理服务测试
//  *
//  * <AUTHOR>
//  * @date 2025/10/23
//  */
// @SpringBootTest
// public class PositionManagementServiceTest {
//
//     @Autowired
//     private PositionManagementService positionManagementService;
//
//     @Test
//     public void testGetPositionsForEmployer() {
//         // 测试用工方查询所有项目岗位
//         PositionManagementQueryDTO queryDTO = new PositionManagementQueryDTO();
//         queryDTO.setPageNo(1);
//         queryDTO.setPageSize(10);
//
//         try {
//             PageResult<PositionManagementPageVO> result = positionManagementService.loadPositionPage(queryDTO);
//
//             System.out.println("用工方岗位查询结果:");
//             System.out.println("总数: " + result.getTotal());
//             System.out.println("当前页: " + result.getPageNo());
//             System.out.println("页大小: " + result.getPageSize());
//             System.out.println("数据条数: " + result.getItems().size());
//
//             for (PositionManagementPageVO vo : result.getItems()) {
//                 System.out.println("岗位: " + vo.getPositionName() +
//                                  " | 项目: " + vo.getProjectName() +
//                                  " | 公司: " + vo.getCompanyName() +
//                                  " | 状态: " + vo.getApproveStatusText());
//             }
//         } catch (Exception e) {
//             System.out.println("用工方岗位查询测试异常: " + e.getMessage());
//             e.printStackTrace();
//         }
//     }
//
//
//     @Test
//     public void testGetPositionsWithFilters() {
//         // 测试带条件的岗位查询
//         PositionManagementQueryDTO queryDTO = new PositionManagementQueryDTO();
//         queryDTO.setPageNo(1);
//         queryDTO.setPageSize(5);
//         queryDTO.setApproveStatus(2); // 查询已通过的岗位
//
//         try {
//             PageResult<PositionManagementPageVO> result = positionManagementService.loadPositionPage(queryDTO);
//
//             System.out.println("条件查询结果 (审批状态=已通过):");
//             System.out.println("总数: " + result.getTotal());
//             System.out.println("数据条数: " + result.getItems().size());
//
//             for (PositionManagementPageVO vo : result.getItems()) {
//                 System.out.println("岗位: " + vo.getPositionName() +
//                                  " | 项目: " + vo.getProjectName() +
//                                  " | 状态: " + vo.getApproveStatusText() +
//                                  " | 预算: " + vo.getTotalBudget() + "/" + vo.getUsedBudget() +
//                                  " | 人数: " + vo.getActualHeadcount() + "/" + vo.getPlannedHeadcount());
//             }
//         } catch (Exception e) {
//             System.out.println("条件查询测试异常: " + e.getMessage());
//             e.printStackTrace();
//         }
//     }
//
//     @Test
//     public void testGetPositionsByProject() {
//         // 测试按项目查询岗位
//         PositionManagementQueryDTO queryDTO = new PositionManagementQueryDTO();
//         queryDTO.setPageNo(1);
//         queryDTO.setPageSize(10);
//         queryDTO.setProjectId("test-project-id"); // 使用测试项目ID
//
//         try {
//             PageResult<PositionManagementPageVO> result = positionManagementService.loadPositionPage(queryDTO);
//
//             System.out.println("项目岗位查询结果:");
//             System.out.println("总数: " + result.getTotal());
//             System.out.println("数据条数: " + result.getItems().size());
//
//             for (PositionManagementPageVO vo : result.getItems()) {
//                 System.out.println("岗位: " + vo.getPositionName() +
//                                  " | 编码: " + vo.getPositionCode() +
//                                  " | 开始日期: " + vo.getStartDate() +
//                                  " | 结束日期: " + vo.getEndDate());
//             }
//         } catch (Exception e) {
//             System.out.println("项目岗位查询测试异常: " + e.getMessage());
//             e.printStackTrace();
//         }
//     }
// }
