package com.caidaocloud.vms.domain.project.enums;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import org.apache.commons.lang3.StringUtils;

public enum DurationType {
    DAY(0, "天"),
    WEEK(1, "周"),
    MONTH(2, "月"),
    YEAR(3, "年");

    private final int value;
    private final String display;

    DurationType(int value, String display) {
        this.value = value;
        this.display = display;
    }

    public int getValue() {
        return value;
    }

    public String getDisplay() {
        return display;
    }

    public static DurationType fromValue(int value) {
        for (DurationType type : DurationType.values()) {
            if (type.value == value) {
                return type;
            }
        }
        throw new ServerException("Invalid duration type value: " + value);
    }


    public static DurationType fromValue(EnumSimple value) {
        if (value == null || StringUtils.isEmpty(value.getValue())) {
            return null;
        }
        try {
            int v = Integer.parseInt(value.getValue());
            for (DurationType type : DurationType.values()) {
                if (type.value == (v)) {
                    return type;
                }
            }
            return null;
        } catch (NumberFormatException e) {
            return null;
        }
    }

    public EnumSimple toEnumSimple() {
        EnumSimple enumSimple = new EnumSimple();
        enumSimple.setValue(String.valueOf(value));
        // enumSimple.setDesc(desc);
        return enumSimple;
    }
}