package com.caidaocloud.vms.application.vo;

import com.caidaocloud.hrpaas.metadata.sdk.dto.JobGradeRange;
import com.caidaocloud.vms.domain.base.enums.ActiveStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 岗位价格库VO
 *
 * <AUTHOR>
 * @date 2025/12/10
 */
@Data
@ApiModel(description = "岗位价格库VO")
public class PositionPriceLibraryVO {

    @ApiModelProperty(value = "主键ID")
    private String bid;

    @ApiModelProperty(value = "岗位ID")
    private String postId;

    @ApiModelProperty(value = "岗位名称")
    private String postName;

    @ApiModelProperty(value = "组织ID")
    private String orgId;

    @ApiModelProperty(value = "组织名称")
    private String orgName;

    @ApiModelProperty("职级")
    private JobGradeRange jobGrade;

    @ApiModelProperty(value = "最低薪资")
    private Integer minSalary;

    @ApiModelProperty(value = "最高薪资")
    private Integer maxSalary;

    @ApiModelProperty(value = "状态")
    private ActiveStatus status;
}
