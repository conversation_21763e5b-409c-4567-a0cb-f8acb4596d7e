package com.caidaocloud.vms.application.vo;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.vms.domain.base.enums.ActiveStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "项目联系人信息 VO")
public class ProjectContactVO {

    @ApiModelProperty(value = "联系人ID")
    private String bid;

    @ApiModelProperty(value = "项目ID")
    private String projectId;

    @ApiModelProperty(value = "员工信息")
    private EmpSimple contact;

    // TODO: 根据联系人员工ID获取员工对应的组织和岗位信息
    @ApiModelProperty(value = "组织信息")
    private String organization;

    @ApiModelProperty(value = "岗位信息")
    private String position;

    @ApiModelProperty(value = "电子邮件")
    private String email;

    @ApiModelProperty(value = "联系电话")
    private String phone;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "联系人状态")
    private ActiveStatus status;
}
