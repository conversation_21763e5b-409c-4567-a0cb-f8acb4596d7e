package com.caidaocloud.vms.domain.project.enums;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import lombok.Getter;

@Getter
public enum ProjectStatus {
    NEW(0, "未提交"),
    SUBMITTED(1, "审批中"),
    APPROVED(2, "已通过"),
    IN_PROGRESS(3, "进行中"),
    CLOSED(4, "已关闭"),
    REJECT(6,"已拒绝");

    private final int code;
    private final String description;

    ProjectStatus(int code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public EnumSimple toEnumSimple() {
        EnumSimple enumSimple = new EnumSimple();
        enumSimple.setValue(String.valueOf(code));
        // enumSimple.setDesc(desc);
        return enumSimple;
    }
    
    public static ProjectStatus fromCode(int code) {
        for (ProjectStatus status : ProjectStatus.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        throw new ServerException("Unknown project status code: " + code);
    }

    public int getCode() {
        return code;
    }
}