package com.caidaocloud.vms.infrastructure.repository;

import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.filter.MultiDataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.vms.domain.project.entity.ProjectDraft;
import com.caidaocloud.vms.domain.project.repository.ProjectDraftRepository;
import com.caidaocloud.vms.domain.project.enums.HistoryType;
import com.caidaocloud.vms.domain.project.enums.OperationType;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 项目草稿Repository实现类
 * 
 * <AUTHOR>
 * @date 2025/10/10
 */
@Repository
public class ProjectDraftRepositoryImpl implements ProjectDraftRepository {

    @Override
    public String saveOrUpdate(ProjectDraft projectDraft) {
        if (projectDraft.getBid() == null) {
            DataInsert.identifier(ProjectDraft.identifier).insert(projectDraft);
        } else {
            DataUpdate.identifier(ProjectDraft.identifier).update(projectDraft);
        }
        return projectDraft.getBid();
    }

    @Override
    public Optional<ProjectDraft> getById(String draftId) {
        return Optional.ofNullable(DataQuery.identifier(ProjectDraft.identifier)
                .oneOrNull(draftId, ProjectDraft.class));
    }

    @Override
    public Optional<ProjectDraft> getByTargetId(String targetId) {
        List<ProjectDraft> drafts = DataQuery.identifier(ProjectDraft.identifier)
                .limit(1, 1)
                .filter(baseFilter()
                        .andEq("targetId", targetId), ProjectDraft.class)
                .getItems();

        return drafts.isEmpty() ? Optional.empty() : Optional.of(drafts.get(0));
    }

    private DataFilter baseFilter() {
        return DataFilter.ne("deleted", Boolean.TRUE.toString());
    }

    @Override
    public List<ProjectDraft> getByProjectIdAndType(String projectId, HistoryType historyType) {
        return DataQuery.identifier(ProjectDraft.identifier)
                .limit(-1, 1)
                .filter(baseFilter()
                        .andEq("projectId", projectId)
                        .andEq("type", historyType.getValue()), ProjectDraft.class)
                .getItems();
    }

    @Override
    public List<ProjectDraft> getByProjectIdAndTypeAndOperation(String projectId, HistoryType historyType,
            OperationType operationType) {
        return DataQuery.identifier(ProjectDraft.identifier)
                .limit(-1, 1)
                .filter(baseFilter()
                        .andEq("projectId", projectId)
                        .andEq("type", historyType.getValue())
                        .andEq("operation", operationType.getValue()), ProjectDraft.class)
                .getItems();
    }

    @Override
    public void delete(String draftId) {
        DataDelete.identifier(ProjectDraft.identifier).delete(draftId);
    }

    @Override
    public void deleteByTargetId(String targetId) {
        List<ProjectDraft> drafts = DataQuery.identifier(ProjectDraft.identifier)
                .limit(-1, 1)
                .filter(baseFilter()
                        .andEq("targetId", targetId), ProjectDraft.class)
                .getItems();

        for (ProjectDraft draft : drafts) {
            delete(draft.getBid());
        }
    }

    @Override
    public void deleteByProjectId(String projectId) {
        List<ProjectDraft> drafts = DataQuery.identifier(ProjectDraft.identifier)
                .limit(-1, 1)
                .filter(baseFilter()
                        .andEq("projectId", projectId), ProjectDraft.class)
                .getItems();

        for (ProjectDraft draft : drafts) {
            delete(draft.getBid());
        }
    }

    @Override
    public List<ProjectDraft> getByPositionId(String positionId) {
        return DataQuery.identifier(ProjectDraft.identifier)
                .limit(-1, 1)
                .filter(baseFilter()
                        .andEq("positionId", positionId), ProjectDraft.class)
                .getItems();
    }

    @Override
    public List<ProjectDraft> getByProjectId(String projectId) {
        return DataQuery.identifier(ProjectDraft.identifier)
                .limit(-1, 1)
                .filter(baseFilter()
                        .andEq("projectId", projectId), ProjectDraft.class)
                .getItems();
    }

    @Override
    public List<ProjectDraft> listByTargetId(List<String> targetIds) {
        if (targetIds == null || targetIds.isEmpty()) {
            return new ArrayList<>();
        }

        return DataQuery.identifier(ProjectDraft.identifier)
                .limit(-1, 1)
                .filter(baseFilter()
                        .andIn("targetId", targetIds), ProjectDraft.class)
                .getItems();
    }

    @Override
    public List<ProjectDraft> listByPositionIds(List<String> positionIds) {
        if (positionIds == null || positionIds.isEmpty()) {
            return new ArrayList<>();
        }

        return DataQuery.identifier(ProjectDraft.identifier)
                .limit(-1, 1)
                .filter(baseFilter()
                        .andIn("positionId", positionIds), ProjectDraft.class)
                .getItems();
    }

    @Override
    public List<ProjectDraft> listByIds(List<String> draftIds) {
        if (draftIds == null || draftIds.isEmpty()) {
            return new ArrayList<>();
        }

        return DataQuery.identifier(ProjectDraft.identifier)
                .limit(-1, 1)
                .filter(baseFilter()
                        .andIn("bid", draftIds), ProjectDraft.class)
                .getItems();
    }
}
