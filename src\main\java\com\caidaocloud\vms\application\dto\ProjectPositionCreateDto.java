package com.caidaocloud.vms.application.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "项目岗位信息")
public class ProjectPositionCreateDto {

    @ApiModelProperty(value = "项目ID", required = true)
    private String projectId;

    @ApiModelProperty(value = "公司ID", required = true)
    private String company;

    @ApiModelProperty(value = "组织ID", required = true)
    private String organization;

    @ApiModelProperty(value = "岗位ID", required = true)
    private String position;
}
