package com.caidaocloud.vms.domain.project.repository;

import com.caidaocloud.vms.domain.project.entity.ProjectContact;

import java.util.List;
import java.util.Optional;

public interface ProjectContactRepository {

    /**
     * 保存或更新项目联系人
     * @param projectContact 项目联系人
     * @return 联系人ID
     */
    String saveOrUpdate(ProjectContact projectContact);

    /**
     * 根据ID获取项目联系人
     * @param contactId 联系人ID
     * @return 项目联系人
     */
    Optional<ProjectContact> getContact(String contactId);

    /**
     * 获取项目的所有联系人
     * @param projectId 项目ID
     * @return 联系人列表
     */
    List<ProjectContact> loadContactList(String projectId);

    /**
     * 删除项目联系人
     * @param contactId 联系人ID
     */
    void deleteContact(ProjectContact contact);
}
