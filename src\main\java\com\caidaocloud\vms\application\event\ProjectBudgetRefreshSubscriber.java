package com.caidaocloud.vms.application.event;

import com.caidaocloud.msg.handler.MessageHandler;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.vms.application.service.ProjectService;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @date 2024/5/15
 */
@Component
@Slf4j
public class ProjectBudgetRefreshSubscriber implements MessageHandler<ProjectBudgetRefreshEvent> {
	@Autowired
	private ProjectService projectService;

	@Override
	public String topic() {
		return ProjectBudgetRefreshEvent.TOPIC;
	}

	@Override
	public void handle(ProjectBudgetRefreshEvent message) throws Exception {
		log.info("ProjectBudgetRefreshEvent handle,msg={}", message);
		try {
			SecurityUserInfo userInfo = new SecurityUserInfo();
			userInfo.setTenantId(message.getTenantId());
			userInfo.setUserId(0L);
			userInfo.setEmpId(0L);
			SecurityUserUtil.setSecurityUserInfo(userInfo);

			projectService.refreshBudget(message.getProjectId());
		}
		catch (Exception e) {
			log.error("ProjectBudgetRefreshEvent handle occurs error", e);
		}
		finally {
			SecurityUserUtil.removeSecurityUserInfo();
		}
		log.info("ProjectBudgetRefreshEvent handle end");
	}
}