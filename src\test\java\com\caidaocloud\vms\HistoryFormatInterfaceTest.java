// package com.caidaocloud.vms;
//
// import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
// import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
// import com.caidaocloud.vms.domain.project.entity.*;
// import com.caidaocloud.vms.domain.project.enums.HistoryType;
// import com.caidaocloud.vms.domain.project.enums.OperationType;
// import com.fasterxml.jackson.databind.ObjectMapper;
// import org.junit.jupiter.api.Test;
//
// import java.util.List;
//
// import static org.junit.jupiter.api.Assertions.*;
//
// /**
//  * HistoryFormat接口实现测试
//  * 验证不同实体类型的历史格式化能力
//  *
//  * <AUTHOR>
//  * @date 2025/10/11
//  */
// public class HistoryFormatInterfaceTest {
//
//     private final ObjectMapper objectMapper = new ObjectMapper();
//
//     @Test
//     public void testProjectDetailedHistoryFormat() {
//         // 测试Project的详细历史格式化
//         try {
//             // 创建Project实例
//             Project project = new Project("PRJ001", "测试项目");
//
//             // 创建ProjectDraft
//             ProjectDraft draft = createProjectDraft(project, HistoryType.BASIC_INFO, OperationType.CREATE);
//
//             // 调用format方法
//             List<ProjectChange> changes = project.format(draft);
//
//             // 验证结果
//             assertNotNull(changes);
//             assertTrue(changes.size() > 0);
//
//             // 验证是详细格式化（应该包含具体的字段变更）
//             boolean hasFieldChanges = changes.stream()
//                 .anyMatch(change -> change.getFieldName() != null &&
//                          !change.getFieldName().startsWith("新增") &&
//                          !change.getFieldName().startsWith("修改") &&
//                          !change.getFieldName().startsWith("删除"));
//
//             assertTrue(hasFieldChanges, "Project应该使用详细格式化，包含具体字段变更");
//
//             System.out.println("Project详细格式化测试通过:");
//             changes.forEach(change ->
//                 System.out.println("  - " + change.getFieldName() + ": " +
//                                  change.getOldValue() + " -> " + change.getNewValue()));
//
//         } catch (Exception e) {
//             fail("Project详细格式化测试失败: " + e.getMessage());
//         }
//     }
//
//     @Test
//     public void testProjectPositionSimplifiedHistoryFormat() {
//         // 测试ProjectPosition的简化历史格式化
//         try {
//             // 创建ProjectPosition实例
//             ProjectPosition position = new ProjectPosition("project123", "Java开发工程师", "测试公司", "技术部");
//
//             // 创建ProjectDraft
//             ProjectDraft draft = createProjectDraft(position, HistoryType.POSITION, OperationType.CREATE);
//
//             // 调用format方法
//             List<ProjectChange> changes = position.format(draft);
//
//             // 验证结果
//             assertNotNull(changes);
//             assertEquals(1, changes.size(), "简化格式化应该只返回一条记录");
//
//             ProjectChange change = changes.get(0);
//             assertNotNull(change.getFieldName());
//             assertTrue(change.getFieldName().contains("新增"), "应该包含操作类型描述");
//             assertTrue(change.getFieldName().contains("岗位"), "应该包含实体类型描述");
//             assertTrue(change.getFieldName().contains("Java开发工程师"), "应该包含岗位名称");
//
//             // 验证是简化格式化（oldValue和newValue应该为null）
//             assertNull(change.getOldValue());
//             assertNull(change.getNewValue());
//
//             System.out.println("ProjectPosition简化格式化测试通过:");
//             System.out.println("  - " + change.getFieldName());
//
//         } catch (Exception e) {
//             fail("ProjectPosition简化格式化测试失败: " + e.getMessage());
//         }
//     }
//
//     @Test
//     public void testProjectSupplierSimplifiedHistoryFormat() {
//         // 测试ProjectSupplier的简化历史格式化
//         try {
//             // 创建ProjectSupplier实例
//             ProjectSupplier supplier = new ProjectSupplier("project123", "supplier456");
//
//             // 创建ProjectDraft
//             ProjectDraft draft = createProjectDraft(supplier, HistoryType.SUPPLIER, OperationType.CREATE);
//
//             // 调用format方法
//             List<ProjectChange> changes = supplier.format(draft);
//
//             // 验证结果
//             assertNotNull(changes);
//             assertEquals(1, changes.size(), "简化格式化应该只返回一条记录");
//
//             ProjectChange change = changes.get(0);
//             assertNotNull(change.getFieldName());
//             assertTrue(change.getFieldName().contains("新增"), "应该包含操作类型描述");
//             assertTrue(change.getFieldName().contains("供应商"), "应该包含实体类型描述");
//             assertTrue(change.getFieldName().contains("supplier456"), "应该包含供应商ID");
//
//             // 验证是简化格式化
//             assertNull(change.getOldValue());
//             assertNull(change.getNewValue());
//
//             System.out.println("ProjectSupplier简化格式化测试通过:");
//             System.out.println("  - " + change.getFieldName());
//
//         } catch (Exception e) {
//             fail("ProjectSupplier简化格式化测试失败: " + e.getMessage());
//         }
//     }
//
//     @Test
//     public void testProjectContactSimplifiedHistoryFormat() {
//         // 测试ProjectContact的简化历史格式化
//         try {
//             // 创建ProjectContact实例
//             ProjectContact contact = new ProjectContact("project123", "emp789", "<EMAIL>", "13800138000");
//
//             // 创建ProjectDraft
//             ProjectDraft draft = createProjectDraft(contact, HistoryType.CONTACT, OperationType.CREATE);
//
//             // 调用format方法
//             List<ProjectChange> changes = contact.format(draft);
//
//             // 验证结果
//             assertNotNull(changes);
//             assertEquals(1, changes.size(), "简化格式化应该只返回一条记录");
//
//             ProjectChange change = changes.get(0);
//             assertNotNull(change.getFieldName());
//             assertTrue(change.getFieldName().contains("新增"), "应该包含操作类型描述");
//             assertTrue(change.getFieldName().contains("联系人"), "应该包含实体类型描述");
//             assertTrue(change.getFieldName().contains("emp789"), "应该包含员工ID");
//
//             // 验证是简化格式化
//             assertNull(change.getOldValue());
//             assertNull(change.getNewValue());
//
//             System.out.println("ProjectContact简化格式化测试通过:");
//             System.out.println("  - " + change.getFieldName());
//
//         } catch (Exception e) {
//             fail("ProjectContact简化格式化测试失败: " + e.getMessage());
//         }
//     }
//
//     @Test
//     public void testHistoryFormatPolymorphism() {
//         // 测试HistoryFormat接口的多态性
//         try {
//             // 创建不同类型的实体
//             HistoryFormat<?>[] entities = {
//                 new Project("PRJ001", "测试项目"),
//                 new ProjectPosition("project123", "Java开发工程师", "测试公司", "技术部"),
//                 new ProjectSupplier("project123", "supplier456"),
//                 new ProjectContact("project123", "emp789", "<EMAIL>", "13800138000")
//             };
//
//             HistoryType[] historyTypes = {
//                 HistoryType.BASIC_INFO,
//                 HistoryType.POSITION,
//                 HistoryType.SUPPLIER,
//                 HistoryType.CONTACT
//             };
//
//             // 测试多态调用
//             for (int i = 0; i < entities.length; i++) {
//                 HistoryFormat<?> entity = entities[i];
//                 HistoryType historyType = historyTypes[i];
//
//                 // 创建草稿
//                 ProjectDraft draft = createProjectDraft(entity, historyType, OperationType.CREATE);
//
//                 // 多态调用format方法
//                 List<ProjectChange> changes = entity.format(draft);
//
//                 // 验证结果
//                 assertNotNull(changes);
//                 assertTrue(changes.size() > 0);
//
//                 System.out.println(entity.getClass().getSimpleName() + " 格式化结果:");
//                 changes.forEach(change ->
//                     System.out.println("  - " + change.getFieldName()));
//             }
//
//             System.out.println("多态性测试通过");
//
//         } catch (Exception e) {
//             fail("多态性测试失败: " + e.getMessage());
//         }
//     }
//
//     /**
//      * 创建ProjectDraft用于测试
//      */
//     private ProjectDraft createProjectDraft(Object entity, HistoryType historyType, OperationType operationType) {
//         try {
//             ProjectDraft draft = new ProjectDraft();
//             draft.setTargetId("test-target-id");
//             draft.setProjectId("test-project-id");
//             draft.setType(historyType.toEnumSimple());
//             draft.setOperation(operationType.toEnumSimple());
//             draft.setSnapshot(objectMapper.writeValueAsString(entity));
//             return draft;
//         } catch (Exception e) {
//             throw new RuntimeException("创建ProjectDraft失败", e);
//         }
//     }
// }
