package com.caidaocloud.vms.application.vo;

import com.caidaocloud.hrpaas.metadata.sdk.dto.Address;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.vms.domain.supplier.enums.SupplierStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(description = "供应商详细信息")
public class SupplierDetailVO {
    @ApiModelProperty(value = "供应商ID")
    private String bid;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "国家/地区")
    private DictSimple country;

    @ApiModelProperty(value = "省份/州")
    private Address province;

    @ApiModelProperty(value = "邮政编码")
    private String postalCode;

    @ApiModelProperty(value = "联系电话")
    private String phone;

    @ApiModelProperty(value = "详细地址")
    private String address;

    @ApiModelProperty(value = "备注信息")
    private String remarks;
    @ApiModelProperty(value = "供应商级别")
    private DictSimple level;

    @ApiModelProperty(value = "员工规模")
    private DictSimple staffSize;

    @ApiModelProperty(value = "营收规模")
    private DictSimple revenueScale;

    @ApiModelProperty(value = "所属行业")
    private List<String> industry;

    @ApiModelProperty(value = "评级", example = "5.0")
    private String rating;
}
