package com.caidaocloud.vms.domain.project.annotation;

import com.caidaocloud.vms.domain.project.enums.HistoryType;
import com.caidaocloud.vms.domain.project.enums.OperationType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Repository工作流注解
 * 标记需要工作流处理的Repository方法
 * 
 * <AUTHOR>
 * @date 2025/9/25
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
// @Deprecated
public @interface HistoryRecord {

    /**
     * 支持的实体类型
     * @return 实体类型数组
     */
    Class<?>[] dtoTypes() ;

    HistoryType historyType() ;

    HistoryType subType() default HistoryType.NONE;

    OperationType operationType();

}
