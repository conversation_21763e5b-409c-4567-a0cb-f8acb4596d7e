package com.caidaocloud.vms.domain.project.repository;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.vms.application.dto.PositionManagementQueryDTO;
import com.caidaocloud.vms.domain.project.entity.ProjectPosition;

import java.util.List;
import java.util.Optional;

public interface ProjectPositionRepository {

    /**
     * 保存或更新项目岗位
     * 
     * @param projectPosition 项目岗位
     * @return 岗位ID
     */
    String saveOrUpdate(ProjectPosition projectPosition);

    /**
     * 根据ID获取项目岗位
     * 
     * @param positionId 岗位ID
     * @return 项目岗位
     */
    Optional<ProjectPosition> getPosition(String positionId);

    /**
     * 获取项目的所有岗位
     * 
     * @param projectId 项目ID
     * @return 岗位列表
     */
    List<ProjectPosition> loadPositionList(String projectId);

    List<ProjectPosition> loadPositionList(String projectId, String positionId);


    /**
     * 获取项目的所有岗位
     * 
     * @param projectId 项目ID
     * @return 岗位列表
     */
    PageResult<ProjectPosition> pagePosition(String projectId, String name, int pageSize, int pageNo);

    /**
     * 删除项目岗位
     *
     * @param positionId 岗位ID
     */
    void deletePosition(ProjectPosition position);



    /**
     * 岗位管理分页
     * @param queryDTO
     * @return
     */
    PageResult<ProjectPosition> positionManagePage(PositionManagementQueryDTO queryDTO);
}
