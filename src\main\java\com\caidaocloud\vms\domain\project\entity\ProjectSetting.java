package com.caidaocloud.vms.domain.project.entity;

import java.util.List;

import com.caidaocloud.vms.domain.project.annotation.DisplayName;
import com.caidaocloud.vms.domain.project.enums.HistoryType;
import com.caidaocloud.vms.domain.project.enums.OperationType;
import com.caidaocloud.vms.domain.project.history.DetailedHistoryFormat;
import lombok.AccessLevel;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
public class ProjectSetting extends DetailedHistoryFormat<ProjectSetting> {

	private String projectId;

	@DisplayName("预算强管控")
	private Boolean budgetEnabled = false;

	@DisplayName("报价强管控")
	private Boolean quoteEnabled = false;

	@DisplayName("项目人员数量强管控")
	private Boolean headcountEnabled = false;

	@DisplayName("岗位满编自动关闭")
	private Boolean positionAutoClose = false;

	@DisplayName("项目到期自动关闭")
	private Boolean projectAutoClose = false;

	@DisplayName("岗位单独审批流")
	private Boolean positionApprovalFlow = false;

	@DisplayName("人员启用预入职")
	private Boolean preHireEnabled = false;

	public static String identifier = "entity.vms.ProjectSetting";

	@Override
	public String getEntityIdentifier() {
		return identifier;
	}

	@Override
	public String formatSummary(List<ProjectChange> changes, OperationType operationType) {
		StringBuilder sb = new StringBuilder();
		for (ProjectChange projectChange : changes) {
			String fieldName = projectChange.getFieldName();
			Boolean b = Boolean.valueOf(projectChange.getNewValue().toString());
			sb.append(b ? "打开" : "关闭").append(fieldName).append(";");
		}
		return sb.toString();
	}
}