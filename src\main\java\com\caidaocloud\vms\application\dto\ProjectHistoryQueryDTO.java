package com.caidaocloud.vms.application.dto;

import com.caidaocloud.dto.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "项目历史查询条件")
public class ProjectHistoryQueryDTO extends BasePage {
    
    @ApiModelProperty(value = "项目ID")
    private String projectId;
    
    @ApiModelProperty(value = "变更类型：0-基本信息，1-供应商，2-联系人，3-岗位，4-设置")
    private String historyType;
    
    @ApiModelProperty(value = "提交人ID")
    private String createBy;
    
    @ApiModelProperty(value = "审批人ID")
    private String approveBy;
    
    @ApiModelProperty(value = "提交开始时间")
    private Long createTimeStart;
    
    @ApiModelProperty(value = "提交结束时间")
    private Long createTimeEnd;
    
    @ApiModelProperty(value = "审批开始时间")
    private Long approveTimeStart;
    
    @ApiModelProperty(value = "审批结束时间")
    private Long approveTimeEnd;
}
