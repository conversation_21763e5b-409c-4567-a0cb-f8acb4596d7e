package com.caidaocloud.vms.domain.employee.enums;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.excption.ServerException;

/**
 * 员工交接类型枚举
 * 
 * <AUTHOR>
 * @date 2025/10/29
 */
public enum EmployeeChangeType {
    PRE_ONBOARDING(0, "预入职"),
    ONBOARDING(1, "入职"),
    RENEWAL(2, "续约"),
    SALARY_ADJUSTMENT(3, "调薪"),
    OFFBOARDING(4, "离职");

    private final int code;
    private final String description;

    EmployeeChangeType(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public EnumSimple toEnumSimple() {
        EnumSimple enumSimple = new EnumSimple();
        enumSimple.setValue(String.valueOf(this.code));
        return enumSimple;
    }

    public static EmployeeChangeType fromCode(int code) {
        for (EmployeeChangeType type : EmployeeChangeType.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        throw new ServerException("Invalid EmployeeChangeType code: " + code);
    }

    public static EmployeeChangeType fromValue(EnumSimple value) {
        if (value == null || value.getValue() == null) {
            return null;
        }
        int code = Integer.parseInt(value.getValue());
        return fromCode(code);
    }
}
