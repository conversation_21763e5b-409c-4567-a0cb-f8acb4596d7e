package com.caidaocloud.vms.application.service;

import java.util.Optional;

import com.caidaocloud.vms.domain.project.entity.ProjectDraft;
import com.caidaocloud.vms.domain.project.repository.ProjectDraftRepository;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 项目设置服务类
 * 
 * <AUTHOR>
 * @date 2025/9/27
 */
@Service
@Slf4j
public class ProjectDraftService {
    
    @Autowired
    private ProjectDraftRepository projectDraftRepository;

    public void saveDraft(ProjectDraft draft) {
        // 检查是否已存在草稿
        Optional<ProjectDraft> existingDraft = projectDraftRepository.getByTargetId(draft.getTargetId());
        if (existingDraft.isPresent()) {
            ProjectDraft ed = existingDraft.get();
            ed.setSnapshot(draft.getSnapshot());
            draft = ed;
        }
        projectDraftRepository.saveOrUpdate(draft);
    }


}
