package com.caidaocloud.vms.application.event;

import com.caidaocloud.hrpaas.metadata.sdk.event.AbstractInteriorEvent;
import com.caidaocloud.security.util.SecurityUserUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * <AUTHOR>
 * @date 2024/5/15
 */
@Data
@Slf4j
public class ProjectBudgetRefreshEvent extends AbstractInteriorEvent {
	public static final String TOPIC = "PORJECT_POSITION";
	private String tenantId;
	private String projectId;

	public ProjectBudgetRefreshEvent(String tenantId, String projectId) {
		super(TOPIC);
		this.tenantId = tenantId;
		this.projectId = projectId;
	}

	public ProjectBudgetRefreshEvent(String projectId) {
		super(TOPIC);
		this.tenantId = SecurityUserUtil.getSecurityUserInfo().getTenantId();
		this.projectId = projectId;
	}

}
