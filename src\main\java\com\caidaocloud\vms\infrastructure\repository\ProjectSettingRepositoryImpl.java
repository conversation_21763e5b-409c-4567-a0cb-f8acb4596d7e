package com.caidaocloud.vms.infrastructure.repository;

import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.vms.domain.project.annotation.HistoryDetailRecord;
import com.caidaocloud.vms.domain.project.entity.PositionRequirement;
import com.caidaocloud.vms.domain.project.entity.ProjectSetting;
import com.caidaocloud.vms.domain.project.enums.HistoryType;
import com.caidaocloud.vms.domain.project.enums.OperationType;
import com.caidaocloud.vms.domain.project.repository.ProjectSettingRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 项目设置Repository实现类
 * 
 * <AUTHOR>
 * @date 2025/9/25
 */
@Repository
public class ProjectSettingRepositoryImpl implements ProjectSettingRepository {

    @Override
    @HistoryDetailRecord(entityTypes = { ProjectSetting.class }, historyType = HistoryType.SETTING,operationType = OperationType.CREATE)
    public String saveOrUpdate(ProjectSetting projectSetting) {
        if (projectSetting.getBid() == null) {
            DataInsert.identifier(ProjectSetting.identifier).insert(projectSetting);
        } else {
            DataUpdate.identifier(ProjectSetting.identifier).update(projectSetting);
        }
        return projectSetting.getBid();
    }

    @Override
    public Optional<ProjectSetting> getByProjectId(String projectId) {
        List<ProjectSetting> settings = DataQuery.identifier(ProjectSetting.identifier)
                .filter(DataFilter.ne("deleted", Boolean.TRUE.toString())
                        .andEq("projectId", projectId), ProjectSetting.class).getItems();

        return settings.isEmpty() ? Optional.empty() : Optional.of(settings.get(0));
    }

    @Override
    public Optional<ProjectSetting> getById(String settingId) {
        return Optional.ofNullable(DataQuery.identifier(ProjectSetting.identifier)
                .oneOrNull(settingId, ProjectSetting.class));
    }

    @Override
    public void delete(String settingId) {
        DataDelete.identifier(ProjectSetting.identifier).delete(settingId);
    }

    @Override
    public void init(ProjectSetting projectSetting, String projectBid) {
        projectSetting.setProjectId(projectBid);
        projectSetting.setBid(projectBid);
        DataInsert.identifier(ProjectSetting.identifier).insert(projectSetting);
    }

    @Override
    public List<ProjectSetting> loadList(List<String> projectIds) {
        return DataQuery.identifier(ProjectSetting.identifier)
                .limit(-1, 1)
                .filter(DataFilter.ne("deleted", Boolean.TRUE.toString())
                        .andIn("projectId", projectIds), ProjectSetting.class).getItems();
    }
}
