package com.caidaocloud.vms.application.vo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "供应商合同信息 VO")
@Data
public class SupplierContractVO {

    @ApiModelProperty(value = "业务ID")
    private String bid;

    @ApiModelProperty(value = "供应商ID")
    private String supplierId;

    @ApiModelProperty(value = "合同名称")
    private String contractName;

    @ApiModelProperty(value = "合同编号")
    private String contractNumber;

    @ApiModelProperty(value = "生效日期（时间戳）")
    private Long effectiveDate;

    @ApiModelProperty(value = "失效日期（时间戳）")
    private Long expiryDate;

    @ApiModelProperty(value = "合同附件")
    private Attachment attachment;
}