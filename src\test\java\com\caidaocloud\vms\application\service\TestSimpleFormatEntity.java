package com.caidaocloud.vms.application.service;

import java.util.List;

import com.caidaocloud.vms.domain.project.entity.ProjectChange;
import com.caidaocloud.vms.domain.project.entity.ProjectDraft;
import com.caidaocloud.vms.domain.project.history.DataSimpleHistoryFormat;
import com.caidaocloud.vms.domain.project.history.SimplifiedHistoryFormat;

/**
 *
 * <AUTHOR>
 * @date 2025/11/3
 */
public class TestSimpleFormatEntity extends SimplifiedHistoryFormat {
	@Override
	public String getEntityIdentifier() {
		return null;
	}

	@Override
	public List<ProjectChange> format(Object originData, ProjectDraft draft) {
		return null;
	}

	@Override
	public String formatDisplay() {
		return null;
	}
}
