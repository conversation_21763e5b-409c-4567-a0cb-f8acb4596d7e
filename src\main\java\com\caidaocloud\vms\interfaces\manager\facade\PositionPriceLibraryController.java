package com.caidaocloud.vms.interfaces.manager.facade;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.vms.application.dto.PositionPriceLibraryDeleteDto;
import com.caidaocloud.web.Result;
import com.caidaocloud.vms.application.dto.PositionPriceLibraryCreateDto;
import com.caidaocloud.vms.application.dto.PositionPriceLibraryQueryDto;
import com.caidaocloud.vms.application.dto.PositionPriceLibrarySalaryRangeDto;
import com.caidaocloud.vms.application.dto.PositionPriceLibraryStatusDto;
import com.caidaocloud.vms.application.dto.PositionPriceLibraryUpdateDto;
import com.caidaocloud.vms.application.service.PositionPriceLibraryService;
import com.caidaocloud.vms.application.vo.PositionPriceLibraryVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 岗位价格库控制器
 *
 * <AUTHOR> Zhou
 * @date 2025/12/10
 */
@RestController
@RequestMapping("/api/vms/v1/manager/position/price")
@Api(tags = "岗位价格库管理")
public class PositionPriceLibraryController {

    @Autowired
    private PositionPriceLibraryService positionPriceLibraryService;

    /**
     * 新建岗位价格库
     */
    @PostMapping("/create")
    @ApiOperation("新建岗位价格库")
    public Result create(
            @ApiParam(value = "创建参数", required = true) @Valid @RequestBody PositionPriceLibraryCreateDto createDto) {
        positionPriceLibraryService.create(createDto);
        return Result.ok();
    }

    /**
     * 根据主键修改薪资范围
     */
    @PostMapping("/update")
    @ApiOperation("修改薪资范围")
    public Result updateSalaryRange(
            @ApiParam(value = "更新参数", required = true) @Valid @RequestBody PositionPriceLibraryUpdateDto updateDto) {
        positionPriceLibraryService.updateSalaryRange(updateDto);
        return Result.ok();
    }

    /**
     * 分页查询价格库
     */
    @PostMapping("/page")
    @ApiOperation("分页查询价格库")
    public Result<PageResult<PositionPriceLibraryVO>> findByPage(
            @ApiParam(value = "查询条件", required = true) @RequestBody PositionPriceLibraryQueryDto queryDto) {
        PageResult<PositionPriceLibraryVO> result = positionPriceLibraryService.findByPage(queryDto);
        return Result.ok(result);
    }

    /**
     * 启用价格库
     */
    @PostMapping("/enable")
    @ApiOperation("启用价格库")
    public Result enable(
            @ApiParam(value = "状态操作参数", required = true) @Valid @RequestBody PositionPriceLibraryStatusDto statusDto) {
        positionPriceLibraryService.enable(statusDto.getBid());
        return Result.ok();
    }


    /**
     * 停用价格库
     */
    @PostMapping("/disable")
    @ApiOperation("停用价格库")
    public Result disable(
            @ApiParam(value = "状态操作参数", required = true) @Valid @RequestBody PositionPriceLibraryStatusDto statusDto) {
        positionPriceLibraryService.disable(statusDto.getBid());
        return Result.ok();
    }


    /**
     * 启用价格库
     */
    @PostMapping("/delete")
    @ApiOperation("启用价格库")
    public Result delete(
            @ApiParam(value = "状态操作参数", required = true) @Valid @RequestBody PositionPriceLibraryDeleteDto dto) {
        positionPriceLibraryService.delete(dto.getBid());
        return Result.ok();
    }

    /**
     * 根据岗位ID和组织ID查找薪资范围
     *
     * @param postId 岗位ID
     * @param orgId  组织ID
     * @return 薪资范围信息
     */
    @GetMapping("/range")
    @ApiOperation("根据岗位ID和组织ID查找薪资范围")
    public Result<PositionPriceLibrarySalaryRangeDto> findSalaryRange(
            @ApiParam(value = "岗位ID", required = true) @RequestParam String postId,
            @ApiParam(value = "组织ID", required = true) @RequestParam String orgId) {
        PositionPriceLibrarySalaryRangeDto result = positionPriceLibraryService.findSalaryRangeByPostIdAndOrgId(postId,
                orgId);
        return Result.ok(result);
    }
}
