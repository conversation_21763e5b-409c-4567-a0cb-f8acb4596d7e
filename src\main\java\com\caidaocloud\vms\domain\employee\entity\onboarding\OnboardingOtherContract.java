package com.caidaocloud.vms.domain.employee.entity.onboarding;

import com.caidaocloud.vms.domain.base.entity.BaseEntity;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class OnboardingOtherContract extends BaseEntity {

    private String empId;

    private Boolean signAgreement = false;

    private Long startDate;

    private Long endDate;

    public static String identifier = "entity.vms.OnboardingOtherContract";

    @Override
    public String getEntityIdentifier() {
        return identifier;
    }

}