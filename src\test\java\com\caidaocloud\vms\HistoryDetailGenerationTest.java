// package com.caidaocloud.vms;
//
// import com.caidaocloud.vms.domain.project.entity.*;
// import com.caidaocloud.vms.domain.project.enums.HistoryType;
// import com.caidaocloud.vms.domain.project.enums.OperationType;
// import com.caidaocloud.vms.domain.project.enums.ChangeDisplayStrategy;
// import com.caidaocloud.vms.domain.project.factory.ProjectHistoryFactory;
// import com.caidaocloud.vms.domain.project.repository.ProjectDraftRepository;
// import com.fasterxml.jackson.databind.ObjectMapper;
// import org.junit.jupiter.api.Test;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.boot.test.context.SpringBootTest;
// import org.springframework.test.context.ActiveProfiles;
//
// import java.math.BigDecimal;
// import java.util.List;
//
// /**
//  * 历史详情记录生成测试
//  *
//  * <AUTHOR>
//  * @date 2025/10/10
//  */
// @SpringBootTest
// @ActiveProfiles("test")
// public class HistoryDetailGenerationTest {
//
//     @Autowired
//     private ProjectHistoryFactory projectHistoryFactory;
//
//     @Autowired
//     private ProjectDraftRepository projectDraftRepository;
//
//     private final ObjectMapper objectMapper = new ObjectMapper();
//
//     /**
//      * 测试HistoryType枚举的变更展示策略
//      */
//     @Test
//     public void testHistoryTypeChangeDisplayStrategy() {
//         // 验证基本信息使用详细展示策略
//         assert HistoryType.BASIC_INFO.getChangeDisplayStrategy() == ChangeDisplayStrategy.DETAILED;
//         assert HistoryType.BASIC_INFO.isDetailedDisplay();
//         assert !HistoryType.BASIC_INFO.isSimplifiedDisplay();
//
//         // 验证供应商使用简化展示策略
//         assert HistoryType.SUPPLIER.getChangeDisplayStrategy() == ChangeDisplayStrategy.SIMPLIFIED;
//         assert !HistoryType.SUPPLIER.isDetailedDisplay();
//         assert HistoryType.SUPPLIER.isSimplifiedDisplay();
//
//         // 验证联系人使用简化展示策略
//         assert HistoryType.CONTACT.getChangeDisplayStrategy() == ChangeDisplayStrategy.SIMPLIFIED;
//         assert !HistoryType.CONTACT.isDetailedDisplay();
//         assert HistoryType.CONTACT.isSimplifiedDisplay();
//
//         // 验证岗位使用简化展示策略
//         assert HistoryType.POSITION.getChangeDisplayStrategy() == ChangeDisplayStrategy.SIMPLIFIED;
//         assert !HistoryType.POSITION.isDetailedDisplay();
//         assert HistoryType.POSITION.isSimplifiedDisplay();
//
//         // 验证设置使用详细展示策略
//         assert HistoryType.SETTING.getChangeDisplayStrategy() == ChangeDisplayStrategy.DETAILED;
//         assert HistoryType.SETTING.isDetailedDisplay();
//         assert !HistoryType.SETTING.isSimplifiedDisplay();
//
//         System.out.println("✓ 测试通过：HistoryType变更展示策略");
//     }
//
//     /**
//      * 测试简化展示策略的变更记录生成（岗位）
//      */
//     @Test
//     public void testSimplifiedChangeGeneration_Position() {
//         // 准备测试数据
//         String projectId = "test_project_simplified_1";
//         String historyId = "test_history_simplified_1";
//
//         // 创建岗位实体
//         ProjectPosition position = new ProjectPosition();
//         position.setBid("position_123");
//         position.setProjectId(projectId);
//         position.setPosition("Java开发工程师");
//         position.setCompany("测试公司");
//         position.setOrganization("技术部");
//
//         // 创建草稿
//         ProjectDraft draft = new ProjectDraft();
//         draft.setTargetId("position_123");
//         draft.setProjectId(projectId);
//         draft.setType(HistoryType.POSITION.toEnumSimple());
//         draft.setOperation(OperationType.CREATE.toEnumSimple());
//
//         try {
//             String snapshot = objectMapper.writeValueAsString(position);
//             draft.setSnapshot(snapshot);
//         } catch (Exception e) {
//             assert false : "序列化实体失败: " + e.getMessage();
//         }
//
//         // 生成历史详情记录
//         ProjectHistoryDetail detail = projectHistoryFactory.generateHistoryDetailFromDraft(
//                 null, draft, historyId);
//
//         // 验证历史详情记录
//         assert detail != null : "历史详情记录不应为空";
//         assert detail.getChange() != null : "变更记录不应为空";
//         assert detail.getChange().size() == 1 : "简化展示应该只有一条变更记录";
//
//         ProjectChange change = detail.getChange().get(0);
//         assert change.getHistoryType() == HistoryType.POSITION : "历史类型应该匹配";
//         assert change.getOperationType() == OperationType.CREATE : "操作类型应该匹配";
//         assert change.getFieldName().contains("新增") : "变更描述应该包含操作类型";
//         assert change.getFieldName().contains("岗位") : "变更描述应该包含实体类型";
//         assert change.getOldValue() == null : "简化展示不应该有旧值";
//         assert change.getNewValue() == null : "简化展示不应该有新值";
//
//         System.out.println("✓ 测试通过：简化展示策略的变更记录生成（岗位）");
//         System.out.println("变更描述：" + change.getFieldName());
//     }
//
//     /**
//      * 测试详细展示策略的变更记录生成（基本信息）
//      */
//     @Test
//     public void testDetailedChangeGeneration_BasicInfo() {
//         // 准备测试数据
//         String projectId = "test_project_detailed_1";
//         String historyId = "test_history_detailed_1";
//
//         // 创建原始项目实体
//         Project originalProject = new Project();
//         originalProject.setBid("project_123");
//         originalProject.setProjectName("原始项目名称");
//         originalProject.setBudget(new BigDecimal("100000"));
//         originalProject.setPlannedHeadcount(10);
//
//         // 创建修改后的项目实体
//         Project updatedProject = new Project();
//         updatedProject.setBid("project_123");
//         updatedProject.setProjectName("修改后的项目名称");
//         updatedProject.setBudget(new BigDecimal("150000"));
//         updatedProject.setPlannedHeadcount(15);
//
//         // 创建草稿
//         ProjectDraft draft = new ProjectDraft();
//         draft.setTargetId("project_123");
//         draft.setProjectId(projectId);
//         draft.setType(HistoryType.BASIC_INFO.toEnumSimple());
//         draft.setOperation(OperationType.UPDATE.toEnumSimple());
//
//         try {
//             String snapshot = objectMapper.writeValueAsString(updatedProject);
//             draft.setSnapshot(snapshot);
//         } catch (Exception e) {
//             assert false : "序列化实体失败: " + e.getMessage();
//         }
//
//         // 生成历史详情记录
//         ProjectHistoryDetail detail = projectHistoryFactory.generateHistoryDetailFromDraft(
//                 originalProject, draft, historyId);
//
//         // 验证历史详情记录
//         assert detail != null : "历史详情记录不应为空";
//         assert detail.getChange() != null : "变更记录不应为空";
//         assert detail.getChange().size() > 1 : "详细展示应该有多条变更记录";
//
//         // 验证具体的字段变更
//         boolean foundProjectNameChange = false;
//         boolean foundBudgetChange = false;
//         boolean foundHeadcountChange = false;
//
//         for (ProjectChange change : detail.getChange()) {
//             assert change.getHistoryType() == HistoryType.BASIC_INFO : "历史类型应该匹配";
//             assert change.getOperationType() == OperationType.UPDATE : "操作类型应该匹配";
//
//             if ("projectName".equals(change.getFieldName())) {
//                 foundProjectNameChange = true;
//                 assert "原始项目名称".equals(change.getOldValue()) : "项目名称旧值应该匹配";
//                 assert "修改后的项目名称".equals(change.getNewValue()) : "项目名称新值应该匹配";
//             } else if ("budget".equals(change.getFieldName())) {
//                 foundBudgetChange = true;
//                 // 注意：BigDecimal比较需要特殊处理
//             } else if ("plannedHeadcount".equals(change.getFieldName())) {
//                 foundHeadcountChange = true;
//                 assert Integer.valueOf(10).equals(change.getOldValue()) : "计划人数旧值应该匹配";
//                 assert Integer.valueOf(15).equals(change.getNewValue()) : "计划人数新值应该匹配";
//             }
//         }
//
//         assert foundProjectNameChange : "应该找到项目名称变更记录";
//         assert foundBudgetChange : "应该找到预算变更记录";
//         assert foundHeadcountChange : "应该找到计划人数变更记录";
//
//         System.out.println("✓ 测试通过：详细展示策略的变更记录生成（基本信息）");
//         System.out.println("变更记录数量：" + detail.getChange().size());
//     }
//
//     /**
//      * 测试供应商简化展示
//      */
//     @Test
//     public void testSimplifiedChangeGeneration_Supplier() {
//         // 准备测试数据
//         String projectId = "test_project_supplier_1";
//         String historyId = "test_history_supplier_1";
//
//         // 创建供应商实体
//         ProjectSupplier supplier = new ProjectSupplier();
//         supplier.setBid("supplier_123");
//         supplier.setProjectId(projectId);
//         supplier.setSupplierName("测试供应商公司");
//         supplier.setContactPerson("张三");
//
//         // 创建草稿
//         ProjectDraft draft = new ProjectDraft();
//         draft.setTargetId("supplier_123");
//         draft.setProjectId(projectId);
//         draft.setType(HistoryType.SUPPLIER.toEnumSimple());
//         draft.setOperation(OperationType.CREATE.toEnumSimple());
//
//         try {
//             String snapshot = objectMapper.writeValueAsString(supplier);
//             draft.setSnapshot(snapshot);
//         } catch (Exception e) {
//             assert false : "序列化实体失败: " + e.getMessage();
//         }
//
//         // 生成历史详情记录
//         ProjectHistoryDetail detail = projectHistoryFactory.generateHistoryDetailFromDraft(
//                 null, draft, historyId);
//
//         // 验证历史详情记录
//         assert detail != null : "历史详情记录不应为空";
//         assert detail.getChange() != null : "变更记录不应为空";
//         assert detail.getChange().size() == 1 : "简化展示应该只有一条变更记录";
//
//         ProjectChange change = detail.getChange().get(0);
//         assert change.getHistoryType() == HistoryType.SUPPLIER : "历史类型应该匹配";
//         assert change.getOperationType() == OperationType.CREATE : "操作类型应该匹配";
//         assert change.getFieldName().contains("新增") : "变更描述应该包含操作类型";
//         assert change.getFieldName().contains("供应商") : "变更描述应该包含实体类型";
//
//         System.out.println("✓ 测试通过：简化展示策略的变更记录生成（供应商）");
//         System.out.println("变更描述：" + change.getFieldName());
//     }
// }
