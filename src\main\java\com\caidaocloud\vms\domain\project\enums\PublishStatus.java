package com.caidaocloud.vms.domain.project.enums;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import org.apache.commons.lang3.StringUtils;

public enum PublishStatus {
    INIT(0, "未发布"),
    PUBLISHED(1, "已发布"),
    ;

    private final int value;
    private final String display;

    PublishStatus(int value, String display) {
        this.value = value;
        this.display = display;
    }

    public int getValue() {
        return value;
    }

    public String getDisplay() {
        return display;
    }

    public static PublishStatus fromValue(int value) {
        for (PublishStatus status : PublishStatus.values()) {
            if (status.value == value) {
                return status;
            }
        }
        throw new ServerException("Unknown value: " + value);
    }

    public static PublishStatus fromValue(EnumSimple value) {
        if (value == null || StringUtils.isEmpty(value.getValue())) {
            return null;
        }
        int v = Integer.parseInt(value.getValue());
        for (PublishStatus status : PublishStatus.values()) {
            if (status.value == v) {
                return status;
            }
        }
        return null;
    }


    public EnumSimple toEnumSimple() {
        EnumSimple enumSimple = new EnumSimple();
        enumSimple.setValue(String.valueOf(value));
        return enumSimple;
    }
}