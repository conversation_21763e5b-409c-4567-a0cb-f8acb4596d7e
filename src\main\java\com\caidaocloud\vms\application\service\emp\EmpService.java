package com.caidaocloud.vms.application.service.emp;

import java.util.List;
import java.util.Optional;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.vms.application.dto.base.EmpInfoDto;
import com.caidaocloud.vms.domain.base.repository.EmpRepository;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 * @date 2025/10/14
 */
@Service
public class EmpService {

	@Autowired
	private EmpRepository empRepository;


	public List<EmpInfoDto> loadEmpList(List<String> empIds){
		return empRepository.loadEmpList(empIds);
	}

	public EmpInfoDto loadEmp(String empId) {
		Optional<EmpInfoDto> optional = empRepository.loadEmp(empId);
		if (!optional.isPresent()) {
			throw new ServerException("Emp info not found,empId=" + empId);
		}
		return optional.get();
	}
}
