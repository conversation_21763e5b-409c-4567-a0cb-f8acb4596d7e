package com.caidaocloud.vms.application.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "岗位供应商查询条件")
public class PositionSupplierQueryDto {

    @ApiModelProperty(value = "岗位ID", required = true)
    private String positionId;

    @ApiModelProperty(value = "供应商名称（模糊查询）")
    private String supplierName;

    @ApiModelProperty(value = "应邀状态：0-发起，1-同意，2-拒绝，3-终止")
    private Integer inviteStatus;
}
