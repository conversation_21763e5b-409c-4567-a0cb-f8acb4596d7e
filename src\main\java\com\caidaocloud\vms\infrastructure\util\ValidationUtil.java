package com.caidaocloud.vms.infrastructure.util;

import java.util.regex.Pattern;

/**
 * 校验工具类
 * 包含：邮箱、中国大陆手机号
 */
public class ValidationUtil {

    // 邮箱正则（支持常见格式，兼容国际域名）
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
            "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$"
    );

    // 中国大陆手机号正则（支持 13x~19x 号段，严格11位）
    private static final Pattern CHINA_MOBILE_PATTERN = Pattern.compile(
            "^1[3-9]\\d{9}$"
    );

    /**
     * 校验邮箱是否合法
     *
     * @param email 待校验的邮箱字符串
     * @return true 表示合法
     */
    public static boolean isValidEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            return false;
        }
        return EMAIL_PATTERN.matcher(email.trim()).matches();
    }

    /**
     * 校验中国大陆手机号是否合法
     *
     * @param mobile 待校验的手机号字符串
     * @return true 表示合法
     */
    public static boolean isValidChinaMobile(String mobile) {
        if (mobile == null || mobile.trim().isEmpty()) {
            return false;
        }
        return CHINA_MOBILE_PATTERN.matcher(mobile.trim()).matches();
    }

}