package com.caidaocloud.vms.domain.project.enums;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import lombok.Getter;

@Getter
public enum PositionStatus {
    /**
     * 未提交状态
     */
    UN_SUBMITTED("未提交"),

    /**
     * 审批中状态
     */
    UNDER_REVIEW("审批中"),

    /**
     * 已撤回状态
     */
    WITHDRAWN("已撤回"),

    /**
     * 已拒绝状态
     */
    REJECTED("已拒绝"),

    /**
     * 已通过状态
     */
    APPROVED("已通过"),

    /**
     * 已发布状态（进行中）
     */
    PUBLISHED("进行中"),

    /**
     * 已关闭状态
     */
    CLOSED("已关闭");

    private final String description;

    PositionStatus(String description) {
        this.description = description;
    }
    //
    // public EnumSimple toEnumSimple() {
    // EnumSimple enumSimple = new EnumSimple();
    // enumSimple.setValue(String.valueOf(code));
    // // enumSimple.setDesc(desc);
    // return enumSimple;
    // }

    // public static PositionStatus fromCode(int code) {
    // for (PositionStatus status : PositionStatus.values()) {
    // if (status.getCode() == code) {
    // return status;
    // }
    // }
    // throw new ServerException("Unknown project status code: " + code);
    // }
    //
    // public int getCode() {
    // return code;
    // }
}