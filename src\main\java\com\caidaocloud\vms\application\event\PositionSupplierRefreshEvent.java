package com.caidaocloud.vms.application.event;

import com.caidaocloud.hrpaas.metadata.sdk.event.AbstractInteriorEvent;
import com.caidaocloud.security.util.SecurityUserUtil;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

/**
 *
 * <AUTHOR>
 * @date 2024/5/15
 */
@Data
@Slf4j
public class PositionSupplierRefreshEvent extends AbstractInteriorEvent {
	public static final String TOPIC = "POSITION_SUPPLIER";
	private String tenantId;
	private String projectId;
	private String supplierId;
	private String positionId;

	public PositionSupplierRefreshEvent() {
		super(TOPIC);
	}

	public PositionSupplierRefreshEvent(String projectId, String supplierId, String positionId) {
		super(TOPIC);
		this.tenantId = SecurityUserUtil.getSecurityUserInfo().getTenantId();
		this.projectId = projectId;
		this.supplierId = supplierId;
		this.positionId = positionId;
	}

	public static PositionSupplierRefreshEvent supplierCreateEvent(String projectId, String supplierId) {
		return new PositionSupplierRefreshEvent(projectId, supplierId, null);
	}

	public static PositionSupplierRefreshEvent positionCreateEvent(String projectId, String positionId) {
		return new PositionSupplierRefreshEvent(projectId, null, positionId);
	}
}
