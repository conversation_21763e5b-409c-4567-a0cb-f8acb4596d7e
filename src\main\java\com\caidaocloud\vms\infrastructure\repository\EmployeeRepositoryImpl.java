package com.caidaocloud.vms.infrastructure.repository;

import java.util.List;
import java.util.Optional;

import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.vms.domain.employee.entity.Employee;
import com.caidaocloud.vms.domain.employee.repository.EmployeeRepository;
import org.springframework.stereotype.Repository;

/**
 * 员工Repository实现类
 * 
 * <AUTHOR>
 * @date 2025/10/30
 */
@Repository
public class EmployeeRepositoryImpl implements EmployeeRepository {

    @Override
    public List<Employee> loadList(List<String> empIds) {
        return DataQuery.identifier(Employee.identifier)
                .limit(-1, 1)
                .filter(DataFilter.ne("deleted", Boolean.TRUE.toString())
                        .andIn("bid", empIds), Employee.class)
                .getItems();
    }

    /**
     * 保存或更新员工信息
     * 
     * @param employee 员工实体
     * @return 员工ID
     */
    public String saveOrUpdate(Employee employee) {
        Optional<Employee> data = employee.getBid() == null ? Optional.empty() : getById(employee.getBid());
        if (!data.isPresent()) {
            DataInsert.identifier(Employee.identifier).insert(employee);
        } else {
            DataUpdate.identifier(Employee.identifier).update(employee);
        }
        return employee.getBid();
    }

    /**
     * 根据ID获取员工信息
     * 
     * @param empId 员工ID
     * @return 员工实体
     */
    public Optional<Employee> getById(String empId) {
        return Optional.ofNullable(DataQuery.identifier(Employee.identifier)
                .oneOrNull(empId, Employee.class));
    }

    /**
     * 根据工号查询员工
     * 
     * @param workno 工号
     * @return 员工实体
     */
    public Employee getByWorkno(String workno) {
        List<Employee> employees = DataQuery.identifier(Employee.identifier)
                .filter(DataFilter.ne("deleted", Boolean.TRUE.toString())
                        .andEq("workno", workno), Employee.class)
                .getItems();
        return employees.isEmpty() ? null : employees.get(0);
    }

    @Override
    public void deleteById(String empId) {
        DataDelete.identifier(Employee.identifier).softDelete(empId);
    }
}
