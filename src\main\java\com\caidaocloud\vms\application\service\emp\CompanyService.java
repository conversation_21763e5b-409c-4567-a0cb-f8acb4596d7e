package com.caidaocloud.vms.application.service.emp;

import java.util.List;

import com.caidaocloud.vms.application.dto.base.CompanyInfoDto;
import com.caidaocloud.vms.domain.base.repository.CompanyRepository;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 * @date 2025/10/15
 */
@Service
public class CompanyService {
	@Autowired
	private CompanyRepository companyRepository;

	public List<CompanyInfoDto> loadCompanyList(List<String> companyIds){
		return companyRepository.loadCompanyList(companyIds);
	}
}
