package com.caidaocloud.vms.application.vo;

import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "岗位招聘要求信息")
public class PositionRequirementVO {
    @ApiModelProperty(value = "要求ID")
    private String bid;

    @ApiModelProperty(value = "岗位ID")
    private String positionId;

    @ApiModelProperty(value = "工作经验要求")
    private DictSimple workExperience;

    @ApiModelProperty(value = "学历要求")
    private DictSimple education;

    @ApiModelProperty(value = "最低薪资")
    private Integer minSalary;

    @ApiModelProperty(value = "最高薪资")
    private Integer maxSalary;

    @ApiModelProperty(value = "岗位描述")
    private String description;

    @ApiModelProperty(value = "技能要求")
    private DictSimple skill;

    @ApiModelProperty(value = "附件")
    private Attachment attachment;
}
