package com.caidaocloud.vms.domain.project.annotation;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.vms.domain.project.enums.HistoryType;
import com.caidaocloud.vms.domain.project.enums.OperationType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Repository工作流注解
 * 标记需要工作流处理的Repository方法
 * 
 * <AUTHOR>
 * @date 2025/9/25
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface HistoryDetailRecord {

    /**
     * 支持的实体类型
     * @return 实体类型数组
     */
    Class<? extends DataSimple>[] entityTypes() default {};

    /**
     * 默认历史记录类型
     * @return 历史记录类型
     */
    HistoryType historyType();

    HistoryType subType() default HistoryType.NONE;

    OperationType operationType();

    // /**
    //  * 描述信息
    //  * @return 描述信息
    //  */
    // String description() default "";
}
