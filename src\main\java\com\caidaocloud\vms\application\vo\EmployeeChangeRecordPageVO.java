package com.caidaocloud.vms.application.vo;

import com.caidaocloud.vms.domain.base.enums.ApprovalStatus;
import com.caidaocloud.vms.domain.employee.enums.EmployeeChangeType;
import com.caidaocloud.vms.domain.project.enums.ProjectStatus;
import com.caidaocloud.vms.domain.project.enums.QuotationMode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 员工入离项分页查询返回VO
 * 
 * <AUTHOR>
 * @date 2025/10/29
 */
@Data
@ApiModel(description = "员工入离项分页查询返回信息")
public class EmployeeChangeRecordPageVO {

    @ApiModelProperty(value = "记录ID")
    private String bid;

    @ApiModelProperty(value = "交接类型")
    private EmployeeChangeType changeType;

    @ApiModelProperty(value = "项目ID")
    private String projectId;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "岗位ID")
    private String positionId;

    @ApiModelProperty(value = "岗位名称")
    private String positionName;

    @ApiModelProperty(value = "公司ID")
    private String companyId;

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "供应商ID")
    private String supplierId;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "员工ID")
    private String empId;

    @ApiModelProperty(value = "员工姓名")
    private String empName;

    @ApiModelProperty(value = "入项日期")
    private Long startTime;

    @ApiModelProperty(value = "离项日期")
    private Long endTime;

    @ApiModelProperty(value = "薪资")
    private String salary;

    @ApiModelProperty(value = "收费模式")
    private QuotationMode quotationMode;

    private String quotationValue;

    @ApiModelProperty(value = "管理费")
    private String managementFee;

    @ApiModelProperty(value = "审批状态")
    private ApprovalStatus approvalStatus;
}
