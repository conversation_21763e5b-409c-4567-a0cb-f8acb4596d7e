package com.caidaocloud.vms.application.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 项目简单信息VO（用于下拉列表）
 * 
 * <AUTHOR>
 * @date 2025/10/24
 */
@Data
@ApiModel(description = "项目简单信息")
public class ProjectSimpleVO {
    
    @ApiModelProperty(value = "项目ID")
    private String bid;
    
    @ApiModelProperty(value = "项目编码")
    private String projectCode;
    
    @ApiModelProperty(value = "项目名称")
    private String projectName;
    
    @ApiModelProperty(value = "开始日期(时间戳)")
    private Long startDate;
    
    @ApiModelProperty(value = "结束日期(时间戳)")
    private Long endDate;

    private ProjectSettingVO setting;
}
