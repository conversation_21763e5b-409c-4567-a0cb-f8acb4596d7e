package com.caidaocloud.vms.application.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 项目设置VO
 * 
 * <AUTHOR>
 * @date 2025/9/27
 */
@Data
@ApiModel(description = "项目设置详细信息")
public class ProjectSettingVO {
    
    @ApiModelProperty(value = "设置ID")
    private String bid;
    
    @ApiModelProperty(value = "项目ID")
    private String projectId;
    
    @ApiModelProperty(value = "是否启用预算控制")
    private Boolean budgetEnabled;
    
    @ApiModelProperty(value = "是否启用报价功能")
    private Boolean quoteEnabled;
    
    @ApiModelProperty(value = "是否启用人数控制")
    private Boolean headcountEnabled;
    
    @ApiModelProperty(value = "岗位是否自动关闭")
    private Boolean positionAutoClose;
    
    @ApiModelProperty(value = "项目是否自动关闭")
    private Boolean projectAutoClose;
    
    @ApiModelProperty(value = "岗位是否需要审批流程")
    private Boolean positionApprovalFlow;
    
    @ApiModelProperty(value = "是否启用预聘功能")
    private Boolean preHireEnabled;
    
    @ApiModelProperty(value = "创建时间")
    private Long createTime;
    
    @ApiModelProperty(value = "更新时间")
    private Long updateTime;
    
    @ApiModelProperty(value = "创建人")
    private String createBy;
    
    @ApiModelProperty(value = "更新人")
    private String updateBy;
}
