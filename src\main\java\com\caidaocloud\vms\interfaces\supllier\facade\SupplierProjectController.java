package com.caidaocloud.vms.interfaces.supllier.facade;

import java.util.List;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.vms.application.dto.PositionManagementQueryDTO;
import com.caidaocloud.vms.application.service.PositionManagementService;
import com.caidaocloud.vms.application.service.ProjectSupplierService;
import com.caidaocloud.vms.application.service.SupplierService;

import com.caidaocloud.vms.application.vo.PositionSelectVO;
import com.caidaocloud.vms.application.vo.ProjectSimpleVO;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * <AUTHOR> Zhou
 * @date 2025/10/29
 */
@RestController
@RequestMapping("/api/vms/v1/supplier/project")
@Api(tags = "供应商项目", description = "供应商查询关联岗位的接口")
public class SupplierProjectController {

	@Autowired
	private ProjectSupplierService projectSupplierService;

	@Autowired
	private PositionManagementService positionManagementService;

	@Autowired
	private SupplierService supplierService;

	/**
	 * 获取当前供应商关联的项目下拉列表
	 *
	 * @return 项目简单信息列表
	 */
	@GetMapping("/selectList")
	@ApiOperation(value = "获取供应商关联的项目列表", notes = "获取当前供应商关联的所有项目，用于下拉选择")
	public Result<List<ProjectSimpleVO>> getSupplierProjects() {
		String supplierId = getCurrentSupplierId();
		if (supplierId == null) {
			throw new RuntimeException("无法获取当前供应商ID");
		}

		List<ProjectSimpleVO> projects = projectSupplierService.loadProjectSelectList(supplierId);
		return Result.ok(projects);
	}

	/**
	 * 根据项目ID查询当前供应商关联的该项目下的岗位下拉列表
	 *
	 * @param projectId 项目ID
	 * @param queryDTO  查询条件
	 * @return 岗位下拉列表
	 */
	@PostMapping("/{projectId}/position/selectList")
	@ApiOperation(value = "查询项目下的岗位下拉列表", notes = "根据项目ID查询当前供应商关联的该项目下的岗位信息，用于下拉选择")
	public Result<List<PositionSelectVO>> getProjectPositions(
			@ApiParam(value = "项目ID", required = true) @PathVariable String projectId,
			@ApiParam(value = "查询条件", required = true) @RequestBody PositionManagementQueryDTO queryDTO) {

		// 获取当前供应商ID
		String currentSupplierId = getCurrentSupplierId();
		if (currentSupplierId == null) {
			// throw new RuntimeException("无法获取当前供应商ID");
		}

		// 设置当前供应商ID和项目ID
		queryDTO.setSupplierId(currentSupplierId);
		queryDTO.setProjectId(projectId);

		List<PositionSelectVO> result = positionManagementService.loadSupplierPositionSelectList(queryDTO);
		return Result.ok(result);
	}

	/**
	 * 获取当前登录供应商ID
	 * 通过当前用户ID查找关联的供应商信息
	 *
	 * @return 供应商ID
	 */
	private String getCurrentSupplierId() {
		// 获取当前登录用户ID
		String userId = String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId());

		// 通过用户ID查找关联的供应商ID
		// 这里通过供应商联系人表的createBy字段来建立用户和供应商的关联关系
		return supplierService.findSupplierIdByUserId(userId);
	}
}
