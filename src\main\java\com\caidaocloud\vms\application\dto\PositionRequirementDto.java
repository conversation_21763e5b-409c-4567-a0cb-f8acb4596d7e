package com.caidaocloud.vms.application.dto;

import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "岗位招聘要求信息")
public class PositionRequirementDto {

    @ApiModelProperty(value = "要求ID")
    private String bid;

    @ApiModelProperty(value = "项目ID", required = true)
    private String projectId;

    @ApiModelProperty(value = "岗位ID", required = true)
    private String positionId;

    @ApiModelProperty(value = "工作经验要求")
    private String workExperience;

    @ApiModelProperty(value = "学历要求")
    private String education;

    @ApiModelProperty(value = "最低薪资")
    private Integer minSalary;

    @ApiModelProperty(value = "最高薪资")
    private Integer maxSalary;

    @ApiModelProperty(value = "岗位描述")
    private String description;

    @ApiModelProperty(value = "技能要求")
    private String skill;

    @ApiModelProperty(value = "附件")
    private Attachment attachment;
}
