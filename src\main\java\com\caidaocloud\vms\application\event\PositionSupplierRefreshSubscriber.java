package com.caidaocloud.vms.application.event;

import com.caidaocloud.hrpaas.metadata.sdk.event.AbstractInteriorEvent;
import com.caidaocloud.msg.handler.MessageHandler;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.vms.application.service.ProjectPositionService;
import com.caidaocloud.vms.application.service.ProjectService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @date 2024/5/15
 */
@Slf4j
@Component
public class PositionSupplierRefreshSubscriber  implements MessageHandler<PositionSupplierRefreshEvent> {
	@Autowired
	private ProjectPositionService projectPositionService;

	@Override
	public String topic() {
		return PositionSupplierRefreshEvent.TOPIC;
	}

	@Override
	public void handle(PositionSupplierRefreshEvent message) throws Exception {
		log.info("PositionSupplierRefreshEvent handle,msg={}", message);
		try {
			SecurityUserInfo userInfo = new SecurityUserInfo();
			userInfo.setTenantId(message.getTenantId());
			userInfo.setUserId(0L);
			userInfo.setEmpId(0L);
			SecurityUserUtil.setSecurityUserInfo(userInfo);

			projectPositionService.refreshPositionSupplier(message.getProjectId(), message.getSupplierId(), message.getPositionId());
		}
		catch (Exception e) {
			log.error("PositionSupplierRefreshEvent handle occurs error", e);
		}
		finally {
			SecurityUserUtil.removeSecurityUserInfo();
		}
		log.info("PositionSupplierRefreshEvent handle end");
	}
}
