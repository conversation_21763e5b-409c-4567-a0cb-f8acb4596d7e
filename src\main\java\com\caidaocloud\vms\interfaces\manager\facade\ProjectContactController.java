package com.caidaocloud.vms.interfaces.manager.facade;

import java.util.List;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.vms.application.dto.ProjectContactDto;
import com.caidaocloud.vms.application.dto.ProjectSupplierDto;
import com.caidaocloud.vms.application.dto.ProjectSupplierQueryDTO;
import com.caidaocloud.vms.application.service.ProjectContactService;
import com.caidaocloud.vms.application.service.ProjectSupplierService;
import com.caidaocloud.vms.application.vo.ProjectContactVO;
import com.caidaocloud.vms.application.vo.ProjectSupplierVO;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 项目供应商关系控制器
 * 
 * <AUTHOR> Zhou
 * @date 2025/6/10
 */
@RestController
@RequestMapping("/api/vms/v1/manager/project/contact")
@Api(tags = "项目联系人")
public class ProjectContactController {

    @Autowired
    private ProjectContactService projectContactService;

    /**
     * 新增项目联系人
     *
     * @param contactDto 联系人信息
     * @return 操作结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增项目联系人", notes = "为项目添加新的联系人")
    public Result saveContact(
            @ApiParam(value = "联系人信息", required = true) @RequestBody ProjectContactDto contactDto) {

        // 手动校验必填字段
        if (contactDto.getProjectId() == null || contactDto.getProjectId().trim().isEmpty()) {
            return Result.fail("项目ID不能为空");
        }
        if (contactDto.getEmpId() == null || contactDto.getEmpId().trim().isEmpty()) {
            return Result.fail("员工ID不能为空");
        }
        if (contactDto.getEmail() == null || contactDto.getEmail().trim().isEmpty()) {
            return Result.fail("邮箱不能为空");
        }
        if (contactDto.getPhone() == null || contactDto.getPhone().trim().isEmpty()) {
            return Result.fail("电话不能为空");
        }

        projectContactService.saveContact(contactDto);
         return Result.ok();


    }

    /**
     * 编辑项目联系人
     *
     * @param contactDto 联系人信息
     * @return 操作结果
     */
    @PostMapping("/edit")
    @ApiOperation(value = "编辑项目联系人", notes = "更新项目的联系人信息")
    public Result editContact(
            @ApiParam(value = "联系人信息", required = true) @RequestBody ProjectContactDto contactDto) {

        projectContactService.editContact(contactDto);
         return Result.ok();


    }

    /**
     * 获取项目所有联系人信息
     *
     * @param projectId 项目ID
     * @return 联系人列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "获取项目联系人列表", notes = "获取指定项目的所有联系人信息")
    public Result<List<ProjectContactVO>> projectContactList(
            @ApiParam(value = "项目ID", required = true) @RequestParam String projectId) {

        List<ProjectContactVO> list = projectContactService.projectContactList(projectId);
        return Result.ok(list);

    }

    /**
     * 启用项目联系人
     *
     * @param contactId 联系人ID
     * @return 操作结果
     */
    @PostMapping("/activate")
    @ApiOperation(value = "启用项目联系人", notes = "将项目联系人状态设置为启用")
    public Result activateContact(
            @ApiParam(value = "联系人ID", required = true) @RequestParam String contactId) {

        projectContactService.activateContact(contactId);
         return Result.ok();


    }

    /**
     * 停用项目联系人
     *
     * @param contactId 联系人ID
     * @return 操作结果
     */
    @PostMapping("/deactivate")
    @ApiOperation(value = "停用项目联系人", notes = "将项目联系人状态设置为停用")
    public Result deactivateContact(
            @ApiParam(value = "联系人ID", required = true) @RequestParam String contactId) {

        projectContactService.deactivateContact(contactId);
         return Result.ok();


    }

    /**
     * 删除项目联系人
     *
     * @param contactId 联系人ID
     * @return 操作结果
     */
    @PostMapping("/delete")
    @ApiOperation(value = "删除项目联系人", notes = "删除指定的项目联系人")
    public Result deleteContact(
            @RequestBody ProjectContactDto dto) {

        projectContactService.deleteContact(dto);
         return Result.ok();


    }

    /**
     * 提交项目联系人变更
     *
     * @param projectId 项目ID
     * @return 操作结果
     */
    @PostMapping("/commit")
    @ApiOperation(value = "提交", notes = "提交并发起工作流")
    public Result commitProjectContact(
            @ApiParam(value = "项目ID", required = true) @RequestBody ProjectContactDto dto) {

        projectContactService.commitProjectContact(dto.getProjectId());
         return Result.ok();

    }
}