package com.caidaocloud.vms.domain.employee.repository;

import java.util.List;
import java.util.Optional;

import com.caidaocloud.vms.domain.employee.entity.Employee;

/**
 * 员工Repository接口
 *
 * <AUTHOR>
 * @date 2025/10/30
 */
public interface EmployeeRepository {

	/**
	 * 批量加载员工列表
	 *
	 * @param empIds 员工ID列表
	 * @return 员工列表
	 */
	List<Employee> loadList(List<String> empIds);

	/**
	 * 保存或更新员工信息
	 *
	 * @param employee 员工实体
	 * @return 员工ID
	 */
	String saveOrUpdate(Employee employee);

	/**
	 * 根据ID获取员工信息
	 *
	 * @param empId 员工ID
	 * @return 员工实体
	 */
	Optional<Employee> getById(String empId);

	/**
	 * 根据工号查询员工
	 *
	 * @param workno 工号
	 * @return 员工实体
	 */
	Employee getByWorkno(String workno);

	/**
	 * 删除员工
	 *
	 * @param empId 员工ID
	 */
	void deleteById(String empId);
}
