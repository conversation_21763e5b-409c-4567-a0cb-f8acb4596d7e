# 岗位供应商联系人编辑接口测试示例

## 接口信息
- **接口路径**: `POST /api/vms/v1/manager/project/position/supplier/editContact`
- **功能**: 编辑岗位供应商的联系人和提供人数，校验所有供应商提供人数总和不超过岗位计划人数

## 测试用例

### 1. 正常编辑联系人和提供人数
```json
POST /api/vms/v1/manager/project/position/supplier/editContact
Content-Type: application/json

{
    "bid": "supplier-relation-001",
    "supplierContact": ["contact-001", "contact-002"],
    "provide": 5
}
```

**预期结果**: 
- 状态码: 200
- 响应: `{"code": 200, "message": "操作成功"}`

### 2. 只编辑联系人（不修改提供人数）
```json
POST /api/vms/v1/manager/project/position/supplier/editContact
Content-Type: application/json

{
    "bid": "supplier-relation-001",
    "supplierContact": ["contact-003", "contact-004", "contact-005"]
}
```

**预期结果**: 
- 状态码: 200
- 响应: `{"code": 200, "message": "操作成功"}`

### 3. 只编辑提供人数（不修改联系人）
```json
POST /api/vms/v1/manager/project/position/supplier/editContact
Content-Type: application/json

{
    "bid": "supplier-relation-001",
    "provide": 8
}
```

**预期结果**: 
- 状态码: 200
- 响应: `{"code": 200, "message": "操作成功"}`

### 4. 提供人数超过计划人数（应该失败）
```json
POST /api/vms/v1/manager/project/position/supplier/editContact
Content-Type: application/json

{
    "bid": "supplier-relation-001",
    "provide": 15
}
```

**预期结果**: 
- 状态码: 500
- 响应: `{"code": 500, "message": "所有供应商提供人数总和(XX)不能超过岗位计划人数(XX)"}`

### 5. 关系ID不存在（应该失败）
```json
POST /api/vms/v1/manager/project/position/supplier/editContact
Content-Type: application/json

{
    "bid": "non-existent-relation",
    "provide": 5
}
```

**预期结果**: 
- 状态码: 500
- 响应: `{"code": 500, "message": "岗位供应商关系不存在"}`

### 6. 关系ID为空（应该失败）
```json
POST /api/vms/v1/manager/project/position/supplier/editContact
Content-Type: application/json

{
    "supplierContact": ["contact-001"],
    "provide": 5
}
```

**预期结果**: 
- 状态码: 500
- 响应: `{"code": 500, "message": "关系ID不能为空"}`

## 测试前准备

### 1. 创建测试数据
确保系统中存在以下测试数据：
- 项目岗位（计划人数为10）
- 供应商关系（已有提供人数）
- 供应商联系人

### 2. 验证现有数据
```sql
-- 查询岗位计划人数
SELECT bid, planned_headcount FROM project_position WHERE bid = 'position-001';

-- 查询当前供应商提供人数
SELECT bid, position_id, provide FROM position_supplier WHERE position_id = 'position-001';

-- 计算当前总提供人数
SELECT position_id, SUM(provide) as total_provide 
FROM position_supplier 
WHERE position_id = 'position-001' 
GROUP BY position_id;
```

## 校验逻辑说明

1. **关系ID校验**: 必须提供有效的岗位供应商关系ID
2. **岗位存在性校验**: 关系对应的岗位必须存在
3. **提供人数校验**: 如果修改了提供人数，会校验所有供应商的提供人数总和不超过岗位计划人数
4. **联系人更新**: 如果提供了联系人列表，会更新供应商的联系人信息
5. **事务性**: 所有操作在同一个事务中执行，确保数据一致性

## 注意事项

- 校验时会排除当前正在编辑的记录，避免重复计算
- 联系人和提供人数可以单独更新，也可以同时更新
- 更新操作会自动设置 `updateTime` 和 `updateBy` 字段
