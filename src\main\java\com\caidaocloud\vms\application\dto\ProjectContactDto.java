package com.caidaocloud.vms.application.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "项目联系人信息")
public class ProjectContactDto {
    
    @ApiModelProperty(value = "联系人ID")
    private String bid;

    @ApiModelProperty(value = "项目ID", required = true)
    private String projectId;

    @ApiModelProperty(value = "员工ID", required = true)
    private String empId;

    @ApiModelProperty(value = "电子邮件", required = true)
    private String email;

    @ApiModelProperty(value = "联系电话", required = true)
    private String phone;

    @ApiModelProperty(value = "备注")
    private String remarks;
}
