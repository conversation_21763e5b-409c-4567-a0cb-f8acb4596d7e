package com.caidaocloud.vms.domain.supplier.entity;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.security.util.SecurityUserUtil;
import lombok.Data;

@Data
public class SupplierTaxInfo extends DataSimple {

    private String supplierId;

    private String companyName;

    private String businessLicense;

    private String taxId;

    private String phone;

    private String address;

    private String bankName;

    private String bankAccount;

    public static String identifier = "entity.vms.SupplierTaxInfo";

    public SupplierTaxInfo() {
        setIdentifier(identifier);
        setTenantId(SecurityUserUtil.getSecurityUserInfo().getTenantId());
        setUpdateTime(System.currentTimeMillis());
        setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
        setCreateTime(getUpdateTime());
        setCreateBy(getUpdateBy());
    }
    //
    // private static SuppliersRepository repository() {
    //     return SpringUtil.getBean(SuppliersRepository.class);
    // }
    //
    // public void delete() {
    //     repository().deleteTaxInfo(supplierId);
    // }
    //
    // public String save() {
    //     setUpdateTime(System.currentTimeMillis());
    //     setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
    //     setCreateTime(getUpdateTime());
    //     setCreateBy(getUpdateBy());
    //     return repository().updateTaxInfo(this);
    // }
}