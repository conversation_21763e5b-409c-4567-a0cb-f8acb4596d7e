package com.caidaocloud.vms.domain.project.dto;

import java.util.ArrayList;
import java.util.List;

import com.caidaocloud.vms.domain.project.entity.ProjectChange;
import com.googlecode.totallylazy.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 格式化结果DTO
 *
 * <AUTHOR>
 * @date 2025/10/17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FormatResult<T> {
    
    /**
     * 格式化后的实体数据
     */
    private T entity;
    
    /**
     * 变更记录列表
     */
    private List<ProjectChange> changes;

    private List<String> draftIdList=new ArrayList<>();

    private FormatResult(T entity, List<ProjectChange> changes) {
        this.entity = entity;
        this.changes = changes;
    }

    /**
     * 创建格式化结果
     * 
     * @param entity 格式化后的实体数据
     * @param changes 变更记录列表
     * @return 格式化结果
     */
    public static <T> FormatResult<T> of(T entity, List<ProjectChange> changes,String draftId) {
        FormatResult result = new FormatResult(entity, changes);
        result.getDraftIdList().add(draftId);
        return result;
    }
    
    /**
     * 创建空的格式化结果
     * 
     * @param entity 实体数据
     * @return 格式化结果
     */
    public static <T> FormatResult<T> empty(T entity) {
        return new FormatResult(entity, Lists.list());
    }

}
