package com.caidaocloud.vms.infrastructure.repository;

import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.vms.domain.project.entity.ProjectContact;
import com.caidaocloud.vms.domain.project.enums.HistoryType;
import com.caidaocloud.vms.domain.project.enums.OperationType;
import com.caidaocloud.vms.domain.project.repository.ProjectContactRepository;
import com.caidaocloud.vms.domain.project.annotation.HistoryDetailRecord;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public class ProjectContactRepositoryImpl implements ProjectContactRepository {

    @Override
    @HistoryDetailRecord(entityTypes = { ProjectContact.class }, historyType = HistoryType.CONTACT,operationType = OperationType.CREATE)
    public String saveOrUpdate(ProjectContact projectContact) {
        if (projectContact.getBid() == null) {
            DataInsert.identifier(ProjectContact.identifier).insert(projectContact);
        } else {
            DataUpdate.identifier(ProjectContact.identifier).update(projectContact);
        }
        return projectContact.getBid();
    }

    @Override
    public Optional<ProjectContact> getContact(String contactId) {
        return Optional.ofNullable(DataQuery.identifier(ProjectContact.identifier)
                .oneOrNull(contactId, ProjectContact.class));
    }

    @Override
    public List<ProjectContact> loadContactList(String projectId) {
        return DataQuery.identifier(ProjectContact.identifier).limit(-1, 1)
                .filter(DataFilter.ne("deleted", Boolean.TRUE.toString())
                        .andEq("projectId", projectId), ProjectContact.class)
                .getItems();
    }

    @Override
    public void deleteContact(ProjectContact contact) {
        DataDelete.identifier(ProjectContact.identifier).delete(contact.getBid());
    }
}
