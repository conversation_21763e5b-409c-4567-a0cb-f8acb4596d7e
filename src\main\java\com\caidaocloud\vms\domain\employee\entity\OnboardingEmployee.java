package com.caidaocloud.vms.domain.employee.entity;

import com.caidaocloud.vms.domain.employee.entity.onboarding.OnboardingContract;
import com.caidaocloud.vms.domain.employee.entity.onboarding.OnboardingEmpPrivateInfo;
import com.caidaocloud.vms.domain.employee.entity.onboarding.OnboardingEmpWorkInfo;
import com.caidaocloud.vms.domain.employee.entity.onboarding.OnboardingOtherContract;
import com.caidaocloud.vms.domain.employee.entity.onboarding.OnboardingSalary;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 * @date 2025/10/30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OnboardingEmployee {
	private OnboardingEmpWorkInfo onboardingEmpWorkInfo;
	private OnboardingEmpPrivateInfo onboardingEmpPrivateInfo;
	private OnboardingContract onboardingContract;
	private OnboardingOtherContract onboardingOtherContract;
	private OnboardingSalary onboardingSalary;
}
