# 岗位状态枚举增强

## 概述
根据业务需求，为 `PositionStatus` 枚举添加了完整的岗位生命周期状态，包括：未提交、审批中、已撤回、已拒绝、已通过、进行中、已关闭。

## 修改内容

### 文件: `src/main/java/com/caidaocloud/vms/domain/project/enums/PositionStatus.java`

**新增状态枚举**:
```java
public enum PositionStatus {
    /**
     * 未提交状态
     */
    UN_SUBMITTED("未提交"),

    /**
     * 审批中状态
     */
    UNDER_REVIEW("审批中"),

    /**
     * 已撤回状态
     */
    WITHDRAWN("已撤回"),

    /**
     * 已拒绝状态
     */
    REJECTED("已拒绝"),

    /**
     * 已通过状态
     */
    APPROVED("已通过"),

    /**
     * 已发布状态（进行中）
     */
    PUBLISHED("进行中"),

    /**
     * 已关闭状态
     */
    CLOSED("已关闭");
}
```

## 状态说明

### 1. 岗位生命周期状态

| 枚举值 | 中文描述 | 业务含义 | 使用场景 |
|--------|----------|----------|----------|
| `UN_SUBMITTED` | 未提交 | 岗位创建但尚未提交审批 | 岗位草稿状态，可以编辑 |
| `UNDER_REVIEW` | 审批中 | 岗位已提交，正在审批流程中 | 不可编辑，等待审批结果 |
| `WITHDRAWN` | 已撤回 | 岗位审批被撤回 | 可重新编辑和提交 |
| `REJECTED` | 已拒绝 | 岗位审批被拒绝 | 可根据反馈修改后重新提交 |
| `APPROVED` | 已通过 | 岗位审批通过 | 可以发布给供应商 |
| `PUBLISHED` | 进行中 | 岗位已发布，正在招聘中 | 供应商可以看到并投标 |
| `CLOSED` | 已关闭 | 岗位招聘结束 | 不再接受新的投标 |

### 2. 状态流转关系

```
UN_SUBMITTED → UNDER_REVIEW → APPROVED → PUBLISHED → CLOSED
     ↓              ↓             ↓
  WITHDRAWN ← WITHDRAWN    WITHDRAWN
     ↓              ↓             ↓
  REJECTED ←  REJECTED     REJECTED
```

**状态流转说明**:
- **未提交** → **审批中**: 提交岗位审批
- **审批中** → **已通过**: 审批通过
- **审批中** → **已拒绝**: 审批拒绝
- **审批中** → **已撤回**: 主动撤回审批
- **已通过** → **进行中**: 发布岗位
- **已通过** → **已撤回**: 撤回已通过的岗位
- **进行中** → **已关闭**: 关闭岗位招聘
- **已拒绝** → **未提交**: 修改后重新提交
- **已撤回** → **未提交**: 修改后重新提交

## 兼容性处理

### 1. 保留原有 PUBLISHED 状态
- 原代码中的 `PositionStatus.PUBLISHED` 继续有效
- 将 `PUBLISHED` 的描述从"已发布"改为"进行中"，更准确地反映业务状态
- 保持了与现有 `publish()` 方法的兼容性

### 2. 现有业务逻辑不受影响
- `ProjectPosition.checkUpdate()` 方法中的状态检查逻辑保持不变
- `ProjectPosition.publish()` 方法继续使用 `PUBLISHED` 状态
- 所有现有的状态判断和业务流程保持兼容

## 业务价值

### 1. 完整的状态管理
- 覆盖了岗位从创建到关闭的完整生命周期
- 提供了清晰的状态流转路径
- 支持审批流程的各种结果状态

### 2. 更好的用户体验
- 用户可以清楚地了解岗位当前所处的状态
- 支持撤回和重新提交等灵活操作
- 状态描述更加直观易懂

### 3. 系统扩展性
- 为后续的工作流集成提供了完整的状态基础
- 支持更复杂的业务规则和权限控制
- 便于统计和报表功能的实现

## 使用示例

### 1. 状态判断
```java
// 检查岗位是否可以编辑
if (position.getStatus() == PositionStatus.UN_SUBMITTED || 
    position.getStatus() == PositionStatus.WITHDRAWN || 
    position.getStatus() == PositionStatus.REJECTED) {
    // 可以编辑
}

// 检查岗位是否可以发布
if (position.getStatus() == PositionStatus.APPROVED) {
    // 可以发布
}
```

### 2. 状态流转
```java
// 提交审批
position.setStatus(PositionStatus.UNDER_REVIEW);

// 审批通过
position.setStatus(PositionStatus.APPROVED);

// 发布岗位
position.publish(); // 设置为 PUBLISHED

// 关闭岗位
position.setStatus(PositionStatus.CLOSED);
```

## 后续建议

### 1. 添加状态流转验证
建议在实体类中添加状态流转的验证逻辑，确保状态变更符合业务规则。

### 2. 完善审批流程
可以基于新的状态枚举，完善审批流程的实现，包括撤回、重新提交等功能。

### 3. 前端状态展示
前端可以根据新的状态枚举，提供更丰富的状态展示和操作按钮。

### 4. 统计和报表
可以基于完整的状态信息，提供岗位状态分布、流转时间等统计功能。
