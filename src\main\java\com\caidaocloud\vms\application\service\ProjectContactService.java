package com.caidaocloud.vms.application.service;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.annotation.PaasTransactional;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.vms.application.dto.base.EmpInfoDto;
import com.caidaocloud.vms.application.dto.ProjectContactDto;
import com.caidaocloud.vms.application.service.emp.EmpService;
import com.caidaocloud.vms.application.vo.ProjectContactVO;
import com.caidaocloud.vms.domain.project.annotation.HistoryRecord;
import com.caidaocloud.vms.domain.project.entity.Project;
import com.caidaocloud.vms.domain.project.entity.ProjectContact;
import com.caidaocloud.vms.domain.project.entity.ProjectHistory;
import com.caidaocloud.vms.domain.project.entity.ProjectHistoryDetail;
import com.caidaocloud.vms.domain.project.enums.HistoryType;
import com.caidaocloud.vms.domain.project.enums.OperationType;
import com.caidaocloud.vms.domain.project.repository.ProjectContactRepository;
import com.caidaocloud.vms.domain.project.repository.ProjectRepository;
import com.caidaocloud.vms.domain.base.enums.ActiveStatus;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Option;
import com.googlecode.totallylazy.Sequences;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import com.caidaocloud.vms.domain.project.entity.ProjectDraft;
import com.caidaocloud.vms.domain.project.repository.ProjectDraftRepository;
import com.caidaocloud.vms.domain.base.service.WorkflowService;
import com.caidaocloud.vms.domain.project.factory.ProjectHistoryFactory;
import com.caidaocloud.vms.domain.base.exception.WorkflowStartException;
import lombok.extern.slf4j.Slf4j;

/**
 * 项目联系人服务
 * 
 * <AUTHOR> Zhou
 * @date 2025/6/5
 */
@Service
@Slf4j
public class ProjectContactService {

	@Autowired
	private ProjectContactRepository projectContactRepository;

	@Autowired
	private ProjectRepository projectRepository;

	@Autowired
	private EmpService empService;

	@Autowired
	private ProjectDraftRepository projectDraftRepository;

	@Autowired
	private WorkflowService workflowService;

	@Autowired
	private ProjectHistoryFactory projectHistoryFactory;

	@Autowired
	private ProjectHistoryService projectHistoryService;

	/**
	 * 保存项目联系人
	 * 
	 * @param contactDto 联系人信息
	 */
	@PaasTransactional
	@HistoryRecord(dtoTypes = {
			ProjectContactDto.class }, historyType = HistoryType.CONTACT, operationType = OperationType.CREATE)
	public void saveContact(ProjectContactDto contactDto) {
		// 获取项目
		Project project = projectRepository.getById(contactDto.getProjectId());
		if (project == null) {
			throw new ServerException("Project not found: " + contactDto.getProjectId());
		}
		EmpInfoDto emp = empService.loadEmp(contactDto.getEmpId());

		// 初始化联系人
		ProjectContact contact = new ProjectContact(
				project.getBid(),
				contactDto.getEmpId(),
				contactDto.getEmail(),
				contactDto.getPhone());
		contact.update(contactDto,emp);

		// 保存联系人
		projectContactRepository.saveOrUpdate(contact);
	}

	/**
	 * 获取项目所有联系人信息
	 * 
	 * @param projectId 项目ID
	 * @return 联系人列表
	 */
	public List<ProjectContactVO> projectContactList(String projectId) {
		// 获取项目所有联系人信息
		Project project = projectRepository.getById(projectId);
		if (project == null) {
			throw new ServerException("Project not found: " + projectId);
		}
		List<ProjectContact> contactList = projectContactRepository.loadContactList(projectId);
		if (contactList.isEmpty()) {
			return new ArrayList<>();
		}

		List<EmpInfoDto> empList = empService.loadEmpList(Sequences.sequence(contactList)
				.map(ProjectContact::getContact).map(EmpSimple::getEmpId)
				.toList());
		// 转换为VO列表
		return contactList.stream()
				.map(contact -> {
					ProjectContactVO vo = ObjectConverter.convert(contact, ProjectContactVO.class);
					vo.setStatus(ActiveStatus.fromCode(Integer.parseInt(contact.getStatus().getValue())));

					Option<EmpInfoDto> empOpt = Sequences.sequence(empList)
							.find(emp -> vo.getContact().getEmpId().equals(emp.getEmpId()));
					if (empOpt.isDefined()) {
						vo.setOrganization(empOpt.get().getOrganizeTxt());
						vo.setPosition(empOpt.get().getPostTxt());
					}
					return vo;
				})
				.collect(Collectors.toList());
	}

	/**
	 * 编辑项目联系人
	 * 
	 * @param contactDto 联系人信息
	 */
	@PaasTransactional
	@HistoryRecord(dtoTypes = {
			ProjectContactDto.class }, historyType = HistoryType.CONTACT, operationType = OperationType.UPDATE)
	public void editContact(ProjectContactDto contactDto) {
		// 查找联系人
		Optional<ProjectContact> contact = projectContactRepository.getContact(contactDto.getBid());

		if (!contact.isPresent()) {
			throw new ServerException("Contact not found: " + contactDto.getBid());
		}
		EmpInfoDto emp = empService.loadEmp(contactDto.getEmpId());


		// 更新联系人信息
		contact.get().update(contactDto,emp);

		// 保存联系人
		projectContactRepository.saveOrUpdate(contact.get());
	}

	/**
	 * 启用项目联系人
	 * 
	 * @param contactId 联系人ID
	 */
	@PaasTransactional
	public void activateContact(String contactId) {
		// 查找联系人
		Optional<ProjectContact> contactOpt = projectContactRepository.getContact(contactId);

		if (!contactOpt.isPresent()) {
			throw new ServerException("Contact not found: " + contactId);
		}

		// 更新联系人状态为启用
		ProjectContact contact = contactOpt.get();
		contact.active();

		// 保存联系人
		projectContactRepository.saveOrUpdate(contact);
	}

	/**
	 * 停用项目联系人
	 * 
	 * @param contactId 联系人ID
	 */
	@PaasTransactional
	public void deactivateContact(String contactId) {
		// 查找联系人
		Optional<ProjectContact> contactOpt = projectContactRepository.getContact(contactId);

		if (!contactOpt.isPresent()) {
			throw new ServerException("Contact not found: " + contactId);
		}

		// 更新联系人状态为停用
		ProjectContact contact = contactOpt.get();
		contact.inactive();

		// 保存联系人
		projectContactRepository.saveOrUpdate(contact);
	}

	/**
	 * 删除项目联系人
	 * 
	 * @param dto 联系人ID
	 */
	@PaasTransactional
	@HistoryRecord(dtoTypes = {
			ProjectContactDto.class }, historyType = HistoryType.CONTACT, operationType = OperationType.DELETE)
	public void deleteContact(ProjectContactDto dto) {
		String contactId = dto.getBid();
		// 查找联系人
		Optional<ProjectContact> contactOpt = projectContactRepository.getContact(contactId);

		if (!contactOpt.isPresent()) {
			throw new ServerException("Contact not found: " + contactId);
		}

		// 删除联系人
		projectContactRepository.deleteContact(contactOpt.get());
	}

	/**
	 * 提交项目联系人变更
	 * 根据draft生成change，保存historyDetail，发起工作流
	 * 无分布式事务，手动回滚
	 *
	 * @param projectId 项目ID
	 */
	public void commitProjectContact(String projectId) {
		// 获取项目信息
		Project project = projectRepository.getById(projectId);
		if (project == null) {
			throw new ServerException("Project not found: " + projectId);
		}

		// 保存的历史记录，用于回滚
		ProjectHistory savedHistory = null;

		try {
			List<ProjectContact> contactList = projectContactRepository.loadContactList(projectId);

			// 获取项目联系人相关的所有草稿
			List<ProjectDraft> drafts = projectDraftRepository.getByProjectIdAndType(projectId, HistoryType.CONTACT);
			if (drafts.isEmpty()) {
				log.info("No contact drafts found for project: " + projectId);
				return;
			}

			ProjectHistoryDetail historyDetail = projectHistoryFactory.generateHistoryDetailFromDraft(
					HistoryType.CONTACT,
					contactList, drafts);
			ProjectHistory history = new ProjectHistory(projectId);
			history.setDetailList(Lists.list(historyDetail));
			projectHistoryService.saveHistory(history);
			savedHistory = history;

			// 发起工作流
			workflowService.startWorkflow(history);

		} catch (WorkflowStartException e) {
			// 工作流启动失败，手动回滚已保存的历史记录
			projectHistoryService.rollbackHistory(savedHistory);
			throw new ServerException("Failed to start workflow for project contact: " + projectId, e);
		} catch (Exception e) {
			throw new ServerException("Failed to commit project contact: " + projectId, e);
		}

	}
}
