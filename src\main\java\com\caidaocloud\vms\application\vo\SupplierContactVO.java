package com.caidaocloud.vms.application.vo;

import com.caidaocloud.vms.domain.base.enums.ActiveStatus;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "供应商联系人信息 VO")
@Data
public class SupplierContactVO {

    @ApiModelProperty(value = "业务ID")
    private String bid;

    @ApiModelProperty(value = "供应商ID")
    private String supplierId;

    @ApiModelProperty(value = "联系人姓名")
    private String contact;

    @ApiModelProperty(value = "联系人职务")
    private String position;

    @ApiModelProperty(value = "电子邮件")
    private String email;

    @ApiModelProperty(value = "联系电话")
    private String phone;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "联系人状态")
    private ActiveStatus status;

    @ApiModelProperty(value = "是否主要联系人")
    private Boolean isPrimary;
}